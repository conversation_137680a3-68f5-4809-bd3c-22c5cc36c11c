version: "3.8"

services:
  developer-api-gateway:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        ENV: ${ENV:-development}
        REPO_URL: ${REPO_URL:-}
        GIT_TOKEN: ${GIT_TOKEN:-}
    container_name: developer-api-gateway
    ports:
      - "8001:8001"
    environment:
      # Application settings
      ENV: ${ENV:-development}
      APP_NAME: ${APP_NAME:-developer-api-gateway}
      DEBUG: ${DEBUG:-false}
      API_V1_STR: ${API_V1_STR:-/api/v1}
      APP_HOST: 0.0.0.0
      APP_PORT: 8001

      # Repository and Git settings
      REPO_URL: ${REPO_URL:-}
      GIT_TOKEN: ${GIT_TOKEN:-}

      # A2A Protocol settings
      A2A_BASE_URL: ${A2A_BASE_URL:-http://localhost:8000}

      # Kafka settings
      KAFKA_BOOTSTRAP_SERVERS: ${KAFKA_BOOTSTRAP_SERVERS:-kafka:9092}
      KAFKA_AGENT_CREATION_TOPIC: ${KAFKA_AGENT_CREATION_TOPIC:-agent_creation_requests}
      KAFKA_AGENT_CHAT_TOPIC: ${KAFKA_AGENT_CHAT_TOPIC:-agent_chat_requests}
      KAFKA_AGENT_RESPONSE_TOPIC: ${KAFKA_AGENT_RESPONSE_TOPIC:-agent_chat_responses}
      KAFKA_AGENT_TASK_TOPIC: ${KAFKA_AGENT_TASK_TOPIC:-agent_task_requests}
      KAFKA_AGENT_QUERY_TOPIC: ${KAFKA_AGENT_QUERY_TOPIC:-agent_query_requests}
      KAFKA_AGENT_SESSION_DELETION_TOPIC: ${KAFKA_AGENT_SESSION_DELETION_TOPIC:-agent_session_deletion_requests}

      # Enhanced Logging Configuration
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_LEVELS: ${LOG_LEVELS:-}
      LOG_FORMAT: ${LOG_FORMAT:-json}
      LOG_INCLUDE_SOURCE: ${LOG_INCLUDE_SOURCE:-false}
      LOG_PERFORMANCE_MODE: ${LOG_PERFORMANCE_MODE:-false}
      LOG_ASYNC_ENABLED: ${LOG_ASYNC_ENABLED:-false}

      # Service Endpoints
      USER_SERVICE_HOST: ${USER_SERVICE_HOST:-user_service}
      USER_SERVICE_PORT: ${USER_SERVICE_PORT:-50052}
      COMMUNICATION_SERVICE_HOST: ${COMMUNICATION_SERVICE_HOST:-admin_service}
      COMMUNICATION_SERVICE_PORT: ${COMMUNICATION_SERVICE_PORT:-50055}
      AGENT_SERVICE_HOST: ${AGENT_SERVICE_HOST:-localhost}
      AGENT_SERVICE_PORT: ${AGENT_SERVICE_PORT:-50057}
      AGENT_SERVICE_URL: ${AGENT_SERVICE_URL:-http://agent-service:8000}
      AGENT_SERVICE_GRPC_ENABLED: ${AGENT_SERVICE_GRPC_ENABLED:-false}

      # Redis settings
      REDIS_HOST: ${REDIS_HOST:-redis}
      REDIS_PORT: ${REDIS_PORT:-6379}
      REDIS_DB: ${REDIS_DB:-0}
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      REDIS_URI: ${REDIS_URI:-}
      REDIS_JWT_ACCESS_EXPIRE_SEC: ${REDIS_JWT_ACCESS_EXPIRE_SEC:-3600}

      # Authentication and Security
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-dev-secret-key-change-in-production-must-be-32-chars-long}
      JWT_ALGORITHM: ${JWT_ALGORITHM:-HS256}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      REFRESH_TOKEN_EXPIRE_DAYS: ${REFRESH_TOKEN_EXPIRE_DAYS:-7}

      # CORS settings
      CORS_ORIGINS: ${CORS_ORIGINS:-["*"]}
      CORS_CREDENTIALS: ${CORS_CREDENTIALS:-true}
      CORS_METHODS: ${CORS_METHODS:-["*"]}
      CORS_HEADERS: ${CORS_HEADERS:-["*"]}

    volumes:
      # Mount logs directory for debugging
      - ./logs:/app/logs
      # Mount data directory for persistent storage
      - app_data:/app/data
      # Mount for development (uncomment for development mode)
      # - .:/app

    networks:
      - developer-api-network

    depends_on:
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # Resource limits for production deployment
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 1G
        reservations:
          cpus: "0.5"
          memory: 512M

    # Security options
    security_opt:
      - no-new-privileges:true

    # Read-only root filesystem for security (with writable volumes)
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp

  # Redis for session management and caching
  redis:
    image: redis:7-alpine
    container_name: developer-api-redis
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    networks:
      - developer-api-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kafka for message queuing
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: developer-api-kafka
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - developer-api-network
    depends_on:
      - zookeeper
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "kafka-broker-api-versions",
          "--bootstrap-server",
          "localhost:9092",
        ]
      interval: 30s
      timeout: 10s
      retries: 3

  # Zookeeper for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: developer-api-zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - developer-api-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "echo", "ruok", "|", "nc", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  app_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  redis_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local

networks:
  developer-api-network:
    driver: bridge
    name: developer-api-network
