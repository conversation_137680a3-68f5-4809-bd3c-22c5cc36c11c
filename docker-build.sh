#!/bin/bash

# Docker build script for developer-api-gateway
# Supports both development and production builds

set -e

# Default values
ENV=${ENV:-development}
TAG=${TAG:-latest}
REGISTRY=${REGISTRY:-}
PUSH=${PUSH:-false}
BUILD_ARGS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --env ENV          Environment (development|production) [default: development]"
    echo "  -t, --tag TAG          Docker image tag [default: latest]"
    echo "  -r, --registry REG     Docker registry prefix"
    echo "  -p, --push             Push image to registry after build"
    echo "  --build-arg ARG        Additional build arguments (can be used multiple times)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --env production --tag v1.0.0 --push"
    echo "  $0 --env development --tag dev-latest"
    echo "  $0 --build-arg REPO_URL=https://github.com/example/repo"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -t|--tag)
            TAG="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -p|--push)
            PUSH=true
            shift
            ;;
        --build-arg)
            BUILD_ARGS="$BUILD_ARGS --build-arg $2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate environment
if [[ "$ENV" != "development" && "$ENV" != "production" ]]; then
    print_error "Environment must be 'development' or 'production'"
    exit 1
fi

# Set image name
if [[ -n "$REGISTRY" ]]; then
    IMAGE_NAME="${REGISTRY}/developer-api-gateway:${TAG}"
else
    IMAGE_NAME="developer-api-gateway:${TAG}"
fi

# Set Dockerfile based on environment
if [[ "$ENV" == "development" ]]; then
    DOCKERFILE="Dockerfile.dev"
    TARGET=""
else
    DOCKERFILE="Dockerfile"
    TARGET="--target production"
fi

print_status "Building Docker image for developer-api-gateway"
print_status "Environment: $ENV"
print_status "Image name: $IMAGE_NAME"
print_status "Dockerfile: $DOCKERFILE"

# Check if Dockerfile exists
if [[ ! -f "$DOCKERFILE" ]]; then
    print_error "Dockerfile not found: $DOCKERFILE"
    exit 1
fi

# Build the image
print_status "Starting Docker build..."

BUILD_CMD="docker build \
    -f $DOCKERFILE \
    $TARGET \
    --build-arg ENV=$ENV \
    $BUILD_ARGS \
    -t $IMAGE_NAME \
    ."

print_status "Build command: $BUILD_CMD"

if eval $BUILD_CMD; then
    print_success "Docker image built successfully: $IMAGE_NAME"
else
    print_error "Docker build failed"
    exit 1
fi

# Push to registry if requested
if [[ "$PUSH" == "true" ]]; then
    if [[ -z "$REGISTRY" ]]; then
        print_warning "No registry specified, skipping push"
    else
        print_status "Pushing image to registry..."
        if docker push "$IMAGE_NAME"; then
            print_success "Image pushed successfully: $IMAGE_NAME"
        else
            print_error "Failed to push image"
            exit 1
        fi
    fi
fi

# Show image information
print_status "Image information:"
docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# Security scan (if available)
if command -v docker &> /dev/null && docker --help | grep -q "scout"; then
    print_status "Running security scan..."
    docker scout cves "$IMAGE_NAME" || print_warning "Security scan failed or not available"
fi

print_success "Build process completed successfully!"

# Show next steps
echo ""
print_status "Next steps:"
if [[ "$ENV" == "development" ]]; then
    echo "  • Run locally: docker run -p 8001:8001 $IMAGE_NAME"
    echo "  • Use docker-compose: docker-compose up"
else
    echo "  • Deploy to production environment"
    echo "  • Update Kubernetes manifests with new image tag"
fi

echo "  • Check logs: docker logs <container_id>"
echo "  • Health check: curl http://localhost:8001/health"
