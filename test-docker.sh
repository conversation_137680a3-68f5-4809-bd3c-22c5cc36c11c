#!/bin/bash

# Test script for Docker setup verification
# Tests both development and production builds

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test configuration
TEST_TAG="test-$(date +%s)"
DEV_IMAGE="developer-api-gateway:dev-${TEST_TAG}"
PROD_IMAGE="developer-api-gateway:prod-${TEST_TAG}"
CONTAINER_NAME="test-developer-api-gateway"
TEST_PORT="8099"

# Cleanup function
cleanup() {
    print_status "Cleaning up test resources..."
    
    # Stop and remove container if running
    if docker ps -q -f name=${CONTAINER_NAME} > /dev/null 2>&1; then
        docker stop ${CONTAINER_NAME} > /dev/null 2>&1 || true
    fi
    
    if docker ps -aq -f name=${CONTAINER_NAME} > /dev/null 2>&1; then
        docker rm ${CONTAINER_NAME} > /dev/null 2>&1 || true
    fi
    
    # Remove test images
    docker rmi ${DEV_IMAGE} > /dev/null 2>&1 || true
    docker rmi ${PROD_IMAGE} > /dev/null 2>&1 || true
    
    print_success "Cleanup completed"
}

# Set trap for cleanup on exit
trap cleanup EXIT

# Function to test Docker build
test_build() {
    local dockerfile=$1
    local image_name=$2
    local env=$3
    
    print_status "Testing $env build with $dockerfile..."
    
    if docker build -f $dockerfile --build-arg ENV=$env -t $image_name . > /dev/null 2>&1; then
        print_success "$env build successful: $image_name"
        return 0
    else
        print_error "$env build failed"
        return 1
    fi
}

# Function to test container startup
test_container() {
    local image_name=$1
    local env=$2
    
    print_status "Testing $env container startup..."
    
    # Start container
    if docker run -d \
        --name ${CONTAINER_NAME} \
        -p ${TEST_PORT}:8001 \
        -e JWT_SECRET_KEY=test-secret \
        -e KAFKA_BOOTSTRAP_SERVERS=localhost:9092 \
        -e REDIS_HOST=localhost \
        -e AGENT_SERVICE_GRPC_ENABLED=false \
        $image_name > /dev/null 2>&1; then
        
        print_success "Container started successfully"
        
        # Wait for container to be ready
        print_status "Waiting for container to be ready..."
        sleep 10
        
        # Test health endpoint
        if curl -f http://localhost:${TEST_PORT}/health > /dev/null 2>&1; then
            print_success "Health check passed"
            return 0
        else
            print_warning "Health check failed (expected if dependencies not available)"
            return 0  # Don't fail the test for this
        fi
    else
        print_error "Container startup failed"
        return 1
    fi
}

# Function to test image security
test_security() {
    local image_name=$1
    
    print_status "Testing image security..."
    
    # Check if running as non-root user
    local user_info=$(docker run --rm $image_name whoami 2>/dev/null || echo "unknown")
    
    if [[ "$user_info" == "appuser" ]]; then
        print_success "Container runs as non-root user: $user_info"
    else
        print_warning "Container user: $user_info (expected: appuser)"
    fi
    
    # Check image size (should be reasonable)
    local image_size=$(docker images $image_name --format "{{.Size}}" | head -1)
    print_status "Image size: $image_size"
    
    return 0
}

# Function to test build script
test_build_script() {
    print_status "Testing build script..."
    
    if [[ -x "./docker-build.sh" ]]; then
        print_success "Build script is executable"
        
        # Test help option
        if ./docker-build.sh --help > /dev/null 2>&1; then
            print_success "Build script help works"
        else
            print_warning "Build script help failed"
        fi
    else
        print_error "Build script is not executable"
        return 1
    fi
    
    return 0
}

# Function to test docker-compose
test_compose() {
    print_status "Testing docker-compose configuration..."
    
    if [[ -f "docker-compose.yml" ]]; then
        print_success "docker-compose.yml exists"
        
        # Validate compose file
        if docker-compose config > /dev/null 2>&1; then
            print_success "docker-compose.yml is valid"
        else
            print_error "docker-compose.yml validation failed"
            return 1
        fi
    else
        print_error "docker-compose.yml not found"
        return 1
    fi
    
    return 0
}

# Main test execution
main() {
    print_status "Starting Docker setup tests for developer-api-gateway"
    print_status "Test tag: $TEST_TAG"
    echo ""
    
    local tests_passed=0
    local tests_total=0
    
    # Test 1: Check required files
    print_status "=== Test 1: Required Files ==="
    tests_total=$((tests_total + 1))
    
    if [[ -f "Dockerfile" && -f "Dockerfile.dev" && -f ".dockerignore" ]]; then
        print_success "All required Docker files present"
        tests_passed=$((tests_passed + 1))
    else
        print_error "Missing required Docker files"
    fi
    echo ""
    
    # Test 2: Development build
    print_status "=== Test 2: Development Build ==="
    tests_total=$((tests_total + 1))
    
    if test_build "Dockerfile.dev" "$DEV_IMAGE" "development"; then
        tests_passed=$((tests_passed + 1))
    fi
    echo ""
    
    # Test 3: Production build
    print_status "=== Test 3: Production Build ==="
    tests_total=$((tests_total + 1))
    
    if test_build "Dockerfile" "$PROD_IMAGE" "production"; then
        tests_passed=$((tests_passed + 1))
    fi
    echo ""
    
    # Test 4: Container startup (using production image)
    print_status "=== Test 4: Container Startup ==="
    tests_total=$((tests_total + 1))
    
    if test_container "$PROD_IMAGE" "production"; then
        tests_passed=$((tests_passed + 1))
    fi
    echo ""
    
    # Test 5: Security checks
    print_status "=== Test 5: Security Checks ==="
    tests_total=$((tests_total + 1))
    
    if test_security "$PROD_IMAGE"; then
        tests_passed=$((tests_passed + 1))
    fi
    echo ""
    
    # Test 6: Build script
    print_status "=== Test 6: Build Script ==="
    tests_total=$((tests_total + 1))
    
    if test_build_script; then
        tests_passed=$((tests_passed + 1))
    fi
    echo ""
    
    # Test 7: Docker Compose
    print_status "=== Test 7: Docker Compose ==="
    tests_total=$((tests_total + 1))
    
    if test_compose; then
        tests_passed=$((tests_passed + 1))
    fi
    echo ""
    
    # Summary
    print_status "=== Test Summary ==="
    print_status "Tests passed: $tests_passed/$tests_total"
    
    if [[ $tests_passed -eq $tests_total ]]; then
        print_success "All tests passed! Docker setup is working correctly."
        return 0
    elif [[ $tests_passed -gt $((tests_total / 2)) ]]; then
        print_warning "Most tests passed, but some issues need attention."
        return 1
    else
        print_error "Multiple tests failed. Please review the Docker setup."
        return 1
    fi
}

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running"
    exit 1
fi

# Run main test function
main
