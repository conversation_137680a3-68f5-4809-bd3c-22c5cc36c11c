version: "3.8"

services:
  developer-api-gateway:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        ENV: production
        REPO_URL: ${REPO_URL:-}
        GIT_TOKEN: ${GIT_TOKEN:-}
    container_name: developer-api-gateway-prod
    ports:
      - "8001:8001"
    environment:
      # Application settings
      ENV: production
      APP_NAME: developer-api-gateway
      DEBUG: false
      API_V1_STR: /api/v1
      APP_HOST: 0.0.0.0
      APP_PORT: 8001

      # Repository and Git settings
      REPO_URL: ${REPO_URL:-}
      GIT_TOKEN: ${GIT_TOKEN:-}

      # A2A Protocol settings
      A2A_BASE_URL: ${A2A_BASE_URL:-http://localhost:8000}

      # Kafka settings
      KAFKA_BOOTSTRAP_SERVERS: ${KAFKA_BOOTSTRAP_SERVERS:-kafka:9092}
      KAFKA_AGENT_CREATION_TOPIC: ${KAFKA_AGENT_CREATION_TOPIC:-agent_creation_requests}
      KAFKA_AGENT_CHAT_TOPIC: ${KAFKA_AGENT_CHAT_TOPIC:-agent_chat_requests}
      KAFKA_AGENT_RESPONSE_TOPIC: ${KAFKA_AGENT_RESPONSE_TOPIC:-agent_chat_responses}
      KAFKA_AGENT_TASK_TOPIC: ${KAFKA_AGENT_TASK_TOPIC:-agent_task_requests}
      KAFKA_AGENT_QUERY_TOPIC: ${KAFKA_AGENT_QUERY_TOPIC:-agent_query_requests}
      KAFKA_AGENT_SESSION_DELETION_TOPIC: ${KAFKA_AGENT_SESSION_DELETION_TOPIC:-agent_session_deletion_requests}

      # Production Logging Configuration
      LOG_LEVEL: INFO
      LOG_LEVELS: INFO,WARNING,ERROR,CRITICAL
      LOG_FORMAT: json
      LOG_INCLUDE_SOURCE: false
      LOG_PERFORMANCE_MODE: false
      LOG_ASYNC_ENABLED: true

      # Service Endpoints
      USER_SERVICE_HOST: ${USER_SERVICE_HOST:-user_service}
      USER_SERVICE_PORT: ${USER_SERVICE_PORT:-50052}
      COMMUNICATION_SERVICE_HOST: ${COMMUNICATION_SERVICE_HOST:-admin_service}
      COMMUNICATION_SERVICE_PORT: ${COMMUNICATION_SERVICE_PORT:-50055}
      AGENT_SERVICE_HOST: ${AGENT_SERVICE_HOST:-localhost}
      AGENT_SERVICE_PORT: ${AGENT_SERVICE_PORT:-50057}
      AGENT_SERVICE_URL: ${AGENT_SERVICE_URL:-http://agent-service:8000}
      AGENT_SERVICE_GRPC_ENABLED: ${AGENT_SERVICE_GRPC_ENABLED:-false}

      # Redis settings
      REDIS_HOST: ${REDIS_HOST:-redis}
      REDIS_PORT: ${REDIS_PORT:-6379}
      REDIS_DB: ${REDIS_DB:-0}
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      REDIS_URI: ${REDIS_URI:-}
      REDIS_JWT_ACCESS_EXPIRE_SEC: ${REDIS_JWT_ACCESS_EXPIRE_SEC:-3600}

      # Authentication and Security (REQUIRED in production)
      JWT_SECRET_KEY: ${JWT_SECRET_KEY}
      JWT_ALGORITHM: ${JWT_ALGORITHM:-HS256}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      REFRESH_TOKEN_EXPIRE_DAYS: ${REFRESH_TOKEN_EXPIRE_DAYS:-7}

      # CORS settings (restrictive for production)
      CORS_ORIGINS: ${CORS_ORIGINS:-["https://yourdomain.com"]}
      CORS_CREDENTIALS: ${CORS_CREDENTIALS:-true}
      CORS_METHODS: ${CORS_METHODS:-["GET","POST","PUT","DELETE"]}
      CORS_HEADERS: ${CORS_HEADERS:-["*"]}

    volumes:
      # Mount logs directory for monitoring
      - ./logs:/app/logs:rw
      # Mount data directory for persistent storage
      - app_data:/app/data:rw

    networks:
      - developer-api-network

    depends_on:
      redis:
        condition: service_healthy
      kafka:
        condition: service_healthy

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # Production resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

    # Enhanced security for production
    security_opt:
      - no-new-privileges:true
      - apparmor:unconfined
    
    # Read-only root filesystem for security
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
      - /var/tmp:noexec,nosuid,size=50m

    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis for session management and caching (Production)
  redis:
    image: redis:7-alpine
    container_name: developer-api-redis-prod
    ports:
      - "6379:6379"
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - developer-api-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # Kafka for message queuing (Production)
  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: developer-api-kafka-prod
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
    volumes:
      - kafka_data:/var/lib/kafka/data
    networks:
      - developer-api-network
    depends_on:
      - zookeeper
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Zookeeper for Kafka (Production)
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: developer-api-zookeeper-prod
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_INIT_LIMIT: 5
      ZOOKEEPER_SYNC_LIMIT: 2
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - developer-api-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "echo", "ruok", "|", "nc", "localhost", "2181"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

volumes:
  app_data:
    driver: local
  redis_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local

networks:
  developer-api-network:
    driver: bridge
    name: developer-api-network-prod
