#!/bin/bash

# Environment setup script for Developer API Gateway
# Creates .env file from .env.example with proper values

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to generate random string
generate_secret() {
    openssl rand -hex 32 2>/dev/null || python3 -c "import secrets; print(secrets.token_hex(32))" 2>/dev/null || echo "change-this-secret-key-in-production-$(date +%s)"
}

# Function to prompt for input with default
prompt_input() {
    local prompt="$1"
    local default="$2"
    local value
    
    if [[ -n "$default" ]]; then
        read -p "$prompt [$default]: " value
        echo "${value:-$default}"
    else
        read -p "$prompt: " value
        echo "$value"
    fi
}

# Function to prompt for password
prompt_password() {
    local prompt="$1"
    local value
    
    read -s -p "$prompt: " value
    echo ""
    echo "$value"
}

print_status "Developer API Gateway Environment Setup"
echo "========================================"
echo ""

# Check if .env already exists
if [[ -f ".env" ]]; then
    print_warning ".env file already exists!"
    read -p "Do you want to overwrite it? (y/N): " overwrite
    if [[ "$overwrite" != "y" && "$overwrite" != "Y" ]]; then
        print_status "Exiting without changes"
        exit 0
    fi
    cp .env .env.backup.$(date +%s)
    print_status "Backup created: .env.backup.$(date +%s)"
fi

# Check if .env.example exists
if [[ ! -f ".env.example" ]]; then
    print_error ".env.example file not found!"
    exit 1
fi

print_status "Setting up environment configuration..."
echo ""

# Environment selection
echo "Select environment:"
echo "1) Development"
echo "2) Production"
read -p "Choose (1-2) [1]: " env_choice

case $env_choice in
    2)
        ENV="production"
        ;;
    *)
        ENV="development"
        ;;
esac

print_status "Environment: $ENV"
echo ""

# Copy .env.example to .env
cp .env.example .env

# Function to update env value
update_env() {
    local key="$1"
    local value="$2"
    
    if [[ -n "$value" ]]; then
        if grep -q "^${key}=" .env; then
            sed -i.bak "s|^${key}=.*|${key}=${value}|" .env
        else
            echo "${key}=${value}" >> .env
        fi
    fi
}

# Basic configuration
update_env "ENV" "$ENV"

# JWT Secret Key (required)
print_status "Generating JWT secret key..."
JWT_SECRET=$(generate_secret)
update_env "JWT_SECRET_KEY" "$JWT_SECRET"
print_success "JWT secret key generated"

# Redis configuration
print_status "Redis Configuration"
REDIS_HOST=$(prompt_input "Redis host" "localhost")
REDIS_PORT=$(prompt_input "Redis port" "6379")
REDIS_DB=$(prompt_input "Redis database" "0")

if [[ "$ENV" == "production" ]]; then
    REDIS_PASSWORD=$(prompt_password "Redis password (leave empty for no password)")
    update_env "REDIS_PASSWORD" "$REDIS_PASSWORD"
fi

update_env "REDIS_HOST" "$REDIS_HOST"
update_env "REDIS_PORT" "$REDIS_PORT"
update_env "REDIS_DB" "$REDIS_DB"

# Kafka configuration
print_status "Kafka Configuration"
KAFKA_SERVERS=$(prompt_input "Kafka bootstrap servers" "localhost:9092")
update_env "KAFKA_BOOTSTRAP_SERVERS" "$KAFKA_SERVERS"

# Service endpoints
print_status "Service Endpoints"
USER_SERVICE_HOST=$(prompt_input "User service host" "user_service")
USER_SERVICE_PORT=$(prompt_input "User service port" "50052")
COMMUNICATION_SERVICE_HOST=$(prompt_input "Communication service host" "admin_service")
COMMUNICATION_SERVICE_PORT=$(prompt_input "Communication service port" "50055")
AGENT_SERVICE_URL=$(prompt_input "Agent service URL" "http://agent-service:8000")

update_env "USER_SERVICE_HOST" "$USER_SERVICE_HOST"
update_env "USER_SERVICE_PORT" "$USER_SERVICE_PORT"
update_env "COMMUNICATION_SERVICE_HOST" "$COMMUNICATION_SERVICE_HOST"
update_env "COMMUNICATION_SERVICE_PORT" "$COMMUNICATION_SERVICE_PORT"
update_env "AGENT_SERVICE_URL" "$AGENT_SERVICE_URL"

# Logging configuration
print_status "Logging Configuration"
if [[ "$ENV" == "production" ]]; then
    update_env "LOG_LEVEL" "INFO"
    update_env "LOG_FORMAT" "json"
    update_env "LOG_ASYNC_ENABLED" "true"
else
    LOG_LEVEL=$(prompt_input "Log level" "DEBUG")
    LOG_FORMAT=$(prompt_input "Log format (text/json)" "text")
    update_env "LOG_LEVEL" "$LOG_LEVEL"
    update_env "LOG_FORMAT" "$LOG_FORMAT"
fi

# CORS configuration for production
if [[ "$ENV" == "production" ]]; then
    print_status "CORS Configuration"
    print_warning "For production, please specify allowed origins"
    CORS_ORIGINS=$(prompt_input "Allowed origins (JSON array)" '["https://yourdomain.com"]')
    update_env "CORS_ORIGINS" "$CORS_ORIGINS"
fi

# Optional configurations
print_status "Optional Configuration"
read -p "Configure repository settings? (y/N): " config_repo
if [[ "$config_repo" == "y" || "$config_repo" == "Y" ]]; then
    REPO_URL=$(prompt_input "Repository URL" "")
    GIT_TOKEN=$(prompt_password "Git token (optional)")
    update_env "REPO_URL" "$REPO_URL"
    update_env "GIT_TOKEN" "$GIT_TOKEN"
fi

# Clean up backup file
rm -f .env.bak

print_success "Environment configuration completed!"
echo ""
print_status "Configuration summary:"
echo "  • Environment: $ENV"
echo "  • Redis: $REDIS_HOST:$REDIS_PORT"
echo "  • Kafka: $KAFKA_SERVERS"
echo "  • User Service: $USER_SERVICE_HOST:$USER_SERVICE_PORT"
echo "  • Agent Service: $AGENT_SERVICE_URL"
echo ""

if [[ "$ENV" == "production" ]]; then
    print_warning "IMPORTANT: Review the .env file before deploying to production!"
    print_warning "Make sure all sensitive values are properly configured."
fi

print_status "Next steps:"
echo "  1. Review the .env file: cat .env"
echo "  2. Start the services: ./deploy.sh up"
echo "  3. Check service status: ./deploy.sh status"
echo "  4. View logs: ./deploy.sh logs"
echo ""
print_success "Setup completed successfully!"
