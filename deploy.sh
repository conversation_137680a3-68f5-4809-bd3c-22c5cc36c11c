#!/bin/bash

# Deployment script for Developer API Gateway
# Supports both development and production deployments

set -e

# Default values
ENV=${ENV:-development}
COMPOSE_FILE=""
BUILD=${BUILD:-true}
PULL=${PULL:-false}
LOGS=${LOGS:-false}
CLEANUP=${CLEANUP:-false}
BACKUP=${BACKUP:-false}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo ""
    echo "Commands:"
    echo "  up          Start the services"
    echo "  down        Stop the services"
    echo "  restart     Restart the services"
    echo "  logs        Show service logs"
    echo "  status      Show service status"
    echo "  backup      Backup data volumes"
    echo "  restore     Restore data volumes"
    echo "  clean       Clean up containers and volumes"
    echo ""
    echo "Options:"
    echo "  -e, --env ENV          Environment (development|production) [default: development]"
    echo "  -f, --file FILE        Docker compose file to use"
    echo "  --no-build             Skip building images"
    echo "  --pull                 Pull latest images before starting"
    echo "  --logs                 Show logs after starting"
    echo "  --cleanup              Clean up before starting"
    echo "  --backup               Create backup before operations"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 up                                    # Start development environment"
    echo "  $0 --env production up                  # Start production environment"
    echo "  $0 --env production --pull up           # Pull and start production"
    echo "  $0 logs                                  # Show logs"
    echo "  $0 --env production backup               # Backup production data"
}

# Parse command line arguments
COMMAND=""
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -f|--file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        --no-build)
            BUILD=false
            shift
            ;;
        --pull)
            PULL=true
            shift
            ;;
        --logs)
            LOGS=true
            shift
            ;;
        --cleanup)
            CLEANUP=true
            shift
            ;;
        --backup)
            BACKUP=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        up|down|restart|logs|status|backup|restore|clean)
            COMMAND="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate command
if [[ -z "$COMMAND" ]]; then
    print_error "No command specified"
    show_usage
    exit 1
fi

# Validate environment
if [[ "$ENV" != "development" && "$ENV" != "production" ]]; then
    print_error "Environment must be 'development' or 'production'"
    exit 1
fi

# Set compose file based on environment if not specified
if [[ -z "$COMPOSE_FILE" ]]; then
    if [[ "$ENV" == "production" ]]; then
        COMPOSE_FILE="docker-compose.prod.yml"
    else
        COMPOSE_FILE="docker-compose.yml"
    fi
fi

# Check if compose file exists
if [[ ! -f "$COMPOSE_FILE" ]]; then
    print_error "Compose file not found: $COMPOSE_FILE"
    exit 1
fi

# Check if .env file exists for production
if [[ "$ENV" == "production" && ! -f ".env" ]]; then
    print_warning "No .env file found. Creating from .env.example..."
    if [[ -f ".env.example" ]]; then
        cp .env.example .env
        print_warning "Please edit .env file with your production values before deploying!"
        exit 1
    else
        print_error "No .env.example file found. Cannot create .env file."
        exit 1
    fi
fi

print_status "Deploying Developer API Gateway"
print_status "Environment: $ENV"
print_status "Compose file: $COMPOSE_FILE"

# Function to run docker-compose commands
run_compose() {
    docker-compose -f "$COMPOSE_FILE" "$@"
}

# Function to create backup
create_backup() {
    print_status "Creating backup..."
    BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup volumes
    if run_compose ps | grep -q "Up"; then
        print_status "Backing up data volumes..."
        docker run --rm -v "$(pwd)":/backup -v developer-api-gateway_redis_data:/data alpine tar czf /backup/"$BACKUP_DIR"/redis_data.tar.gz -C /data .
        docker run --rm -v "$(pwd)":/backup -v developer-api-gateway_kafka_data:/data alpine tar czf /backup/"$BACKUP_DIR"/kafka_data.tar.gz -C /data .
        print_success "Backup created in $BACKUP_DIR"
    else
        print_warning "Services not running, skipping backup"
    fi
}

# Function to restore backup
restore_backup() {
    print_status "Available backups:"
    ls -la ./backups/ 2>/dev/null || print_warning "No backups found"
    echo "Please specify backup directory manually if needed"
}

# Function to cleanup
cleanup() {
    print_status "Cleaning up..."
    run_compose down --volumes --remove-orphans
    docker system prune -f
    print_success "Cleanup completed"
}

# Execute command
case $COMMAND in
    up)
        if [[ "$BACKUP" == "true" ]]; then
            create_backup
        fi
        
        if [[ "$CLEANUP" == "true" ]]; then
            cleanup
        fi
        
        if [[ "$PULL" == "true" ]]; then
            print_status "Pulling latest images..."
            run_compose pull
        fi
        
        print_status "Starting services..."
        if [[ "$BUILD" == "true" ]]; then
            run_compose up --build -d
        else
            run_compose up -d
        fi
        
        print_success "Services started successfully"
        
        # Wait for services to be healthy
        print_status "Waiting for services to be healthy..."
        sleep 10
        
        # Check service status
        run_compose ps
        
        if [[ "$LOGS" == "true" ]]; then
            print_status "Showing logs..."
            run_compose logs -f
        fi
        ;;
        
    down)
        print_status "Stopping services..."
        run_compose down
        print_success "Services stopped"
        ;;
        
    restart)
        print_status "Restarting services..."
        run_compose restart
        print_success "Services restarted"
        ;;
        
    logs)
        print_status "Showing logs..."
        run_compose logs -f
        ;;
        
    status)
        print_status "Service status:"
        run_compose ps
        echo ""
        print_status "Service health:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        ;;
        
    backup)
        create_backup
        ;;
        
    restore)
        restore_backup
        ;;
        
    clean)
        cleanup
        ;;
        
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac

print_success "Operation completed successfully!"

# Show next steps
echo ""
print_status "Next steps:"
case $COMMAND in
    up)
        echo "  • Check service health: $0 status"
        echo "  • View logs: $0 logs"
        echo "  • API documentation: http://localhost:8001/docs"
        echo "  • Health check: curl http://localhost:8001/health"
        ;;
    down)
        echo "  • Start services: $0 up"
        ;;
    *)
        echo "  • Check status: $0 status"
        ;;
esac
