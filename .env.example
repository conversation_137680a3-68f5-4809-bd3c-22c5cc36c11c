# =============================================================================
# DEVELOPER API GATEWAY ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains all environment variables needed to run the Developer API Gateway.
# Copy this file to .env and update the values according to your environment.
#
# SECURITY WARNING: Never commit real credentials to version control!
# Use placeholder values in this file and set real values in your .env file.
# =============================================================================

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment (dev, staging, production)
ENV=dev

# Application name and basic configuration
APP_NAME=developer-api-gateway

# Enable debug mode (set to false in production)
DEBUG=false

# API version prefix
API_V1_STR=/api/v1

# =============================================================================
# REPOSITORY AND GIT CONFIGURATION
# =============================================================================
# Used for gRPC code generation and repository access

# Repository URL for gRPC proto files
REPO_URL=

# Git token for private repository access (if needed)
# SECURITY WARNING: Keep this secret! Use a personal access token with minimal permissions.
GIT_TOKEN=

# =============================================================================
# A2A (Agent-to-Agent) PROTOCOL CONFIGURATION
# =============================================================================

# Base URL for A2A protocol endpoints
A2A_BASE_URL=http://localhost:8000

# =============================================================================
# KAFKA MESSAGE BROKER CONFIGURATION
# =============================================================================
# Kafka is used for asynchronous agent communication, session management,
# and streaming responses. Required for agent operations and chat functionality.

# Kafka bootstrap servers (comma-separated list)
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# Kafka topic names for different message types
KAFKA_AGENT_CREATION_TOPIC=agent_creation_requests
KAFKA_AGENT_CHAT_TOPIC=agent_chat_requests
KAFKA_AGENT_RESPONSE_TOPIC=agent_chat_responses
KAFKA_AGENT_TASK_TOPIC=agent_task_requests
KAFKA_AGENT_QUERY_TOPIC=agent_query_requests
KAFKA_AGENT_SESSION_DELETION_TOPIC=agent_session_deletion_requests

# =============================================================================
# ENHANCED LOGGING CONFIGURATION
# =============================================================================

# Single log level (fallback mode) - DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=DEBUG

# Multi-level logging support (comma-separated list of levels)
# Examples:
#   LOG_LEVELS=DEBUG,ERROR          # Only debug and error messages
#   LOG_LEVELS=INFO,WARNING,ERROR   # Info, warning, and error messages
#   LOG_LEVELS=DEBUG,INFO,WARNING,ERROR,CRITICAL  # All levels
# Leave empty to use LOG_LEVEL instead
LOG_LEVELS=

# Log output format (json or text)
# - json: Structured JSON format with full context (recommended for production)
# - text: Human-readable format (recommended for development)
LOG_FORMAT=text

# Include source code location in text format logs (file, function, line number)
# Only applies when LOG_FORMAT=text
LOG_INCLUDE_SOURCE=false

# Include performance metrics in log output (execution time, memory usage, etc.)
# Adds overhead but useful for performance monitoring
LOG_PERFORMANCE_MODE=false

# Enable asynchronous logging for high-throughput scenarios
# Improves performance by processing logs in background
LOG_ASYNC_ENABLED=false

# =============================================================================
# SERVICE ENDPOINTS
# =============================================================================

# User Service (gRPC)
USER_SERVICE_HOST=user_service
USER_SERVICE_PORT=50052

# Communication Service (gRPC) - handles conversations and messaging
COMMUNICATION_SERVICE_HOST=admin_service
COMMUNICATION_SERVICE_PORT=50055

# Agent Service (gRPC) - for future use if gRPC server is implemented
AGENT_SERVICE_HOST=localhost
AGENT_SERVICE_PORT=50057

# Agent Service (HTTP)
AGENT_SERVICE_URL=http://agent-service:8000

# Agent Service gRPC enabled/disabled
AGENT_SERVICE_GRPC_ENABLED=false

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis is used for caching, session storage, and rate limiting

# Redis connection details
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# Redis password (leave empty if no password is set)
REDIS_PASSWORD=

# Redis URI (alternative to individual settings above)
# Format: redis://[password@]host:port/db
# Example: redis://mypassword@localhost:6379/0
REDIS_URI=

# JWT token expiration in Redis (seconds)
REDIS_JWT_ACCESS_EXPIRE_SEC=3600

# =============================================================================
# AUTHENTICATION AND SECURITY
# =============================================================================

# JWT Secret Key - MUST be at least 32 characters long
# SECURITY WARNING: Generate a strong, unique secret key for production!
# You can generate one using: openssl rand -hex 32
JWT_SECRET_KEY=your-secret-key-at-least-32-chars-long

# JWT algorithm
JWT_ALGORITHM=HS256

# Token expiration times
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# =============================================================================
# CORS (Cross-Origin Resource Sharing) SETTINGS
# =============================================================================

# Allowed origins (comma-separated list or JSON array)
CORS_ORIGINS=["*"]

# Allow credentials in CORS requests
CORS_CREDENTIALS=true

# Allowed HTTP methods
CORS_METHODS=["*"]

# Allowed headers
CORS_HEADERS=["*"]

# =============================================================================
# LOGGING EXAMPLES AND USE CASES
# =============================================================================
#
# Development Setup (verbose logging):
#   LOG_LEVEL=DEBUG
#   LOG_FORMAT=text
#   LOG_INCLUDE_SOURCE=true
#   LOG_PERFORMANCE_MODE=false
#
# Production Setup (optimized):
#   LOG_LEVEL=INFO
#   LOG_FORMAT=json
#   LOG_INCLUDE_SOURCE=false
#   LOG_PERFORMANCE_MODE=false
#   LOG_ASYNC_ENABLED=true
#
# Debug Only (troubleshooting):
#   LOG_LEVELS=DEBUG,ERROR
#   LOG_FORMAT=text
#   LOG_INCLUDE_SOURCE=true
#   LOG_PERFORMANCE_MODE=true
#
# Error Monitoring (alerts only):
#   LOG_LEVELS=ERROR,CRITICAL
#   LOG_FORMAT=json
#   LOG_PERFORMANCE_MODE=false
# =============================================================================

# =============================================================================
# NOTES
# =============================================================================
#
# The LOGGERS configuration in config.py is handled as a dictionary and cannot
# be directly configured via environment variables. It sets default log levels
# for specific components:
#
# - app: INFO
# - uvicorn: INFO
# - fastapi: INFO
# - aiokafka: WARNING
# - sqlalchemy: WARNING
# - httpx: WARNING
# - asyncio: WARNING
# - app.api.routers: INFO
# - app.services: INFO
# - app.middleware: INFO
# - app.core: INFO
#
# These can be overridden in the application code if needed.
# =============================================================================