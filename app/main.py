"""Communication Service gRPC Server

This module implements a gRPC server for the communication service with health checking capabilities.
It provides both the main service functionality and health status reporting.
"""

# Standard library imports
from concurrent import futures

# Third-party imports
import grpc
from grpc_health.v1 import health, health_pb2, health_pb2_grpc

# Local imports
from app.core.config import settings
from app.db import close_db
from app.db.mongo import init_mongo_from_env
from app.grpc_ import communication_pb2_grpc
from app.services.communication_service import CommunicationService
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/main.py")


# Cleanup function
def cleanup() -> None:
    """
    Cleanup resources before server shutdown.

    This function performs cleanup operations such as closing database connections
    when the server is shutting down.
    """
    try:
        # Close MongoDB connections
        close_db()
        logger.info("Closed MongoDB connections")

    except Exception as e:
        # Log error
        logger.error("Error during cleanup", error=str(e))

        # Raise error
        raise RuntimeError("Error during cleanup") from e


# Serve function
def serve() -> None:
    """
    Initialize and run the gRPC server for the communication service.

    This function sets up a gRPC server with a ThreadPoolExecutor for handling
    concurrent requests. The server includes health checking service and the main
    communication service functionality.

    Environment Variables:
        GRPC_PORT (str): The port number for the gRPC server (default: "50055")

    Raises:
        Exception: If server initialization or startup fails
    """
    try:
        # Initialize MongoDB connection from environment variables
        init_mongo_from_env()

        # Create gRPC server with thread pool
        server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

        # Add health checking service
        health_servicer = health.HealthServicer(
            experimental_non_blocking=True,
            experimental_thread_pool=futures.ThreadPoolExecutor(max_workers=2),
        )
        health_pb2_grpc.add_HealthServicer_to_server(health_servicer, server)

        # Set initial health status
        health_servicer.set("", health_pb2.HealthCheckResponse.SERVING)
        health_servicer.set("grpc.health.v1.Health", health_pb2.HealthCheckResponse.SERVING)

        # Add the communication service & set health status for communication service
        communication_pb2_grpc.add_CommunicationServiceServicer_to_server(
            CommunicationService(), server
        )
        health_servicer.set(
            "communication.communicationService", health_pb2.HealthCheckResponse.SERVING
        )

        # Configure server port
        port = settings.GRPC_PORT
        server.add_insecure_port(f"[::]:{port}")

        # Start server
        server.start()
        logger.info("Communication service started", port=port)
        logger.info("Health checking enabled")

        # Keep server running
        server.wait_for_termination()

    except Exception as e:
        # Log error
        logger.error("Server startup failed", error=str(e))

        # Raise error
        raise

    finally:
        # Cleanup resources
        cleanup()


# Main entry point
if __name__ == "__main__":
    # Log the start message
    logger.info("Starting communication service")

    # Serve the communication service
    serve()
