"""
Agent execution schemas for the developer API gateway.

This module provides comprehensive Pydantic models for agent execution requests and responses
with detailed Swagger documentation, examples, and validation rules.
"""

from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, validator


class AgentExecutionStatus(str, Enum):
    """Enumeration of possible agent execution statuses."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(str, Enum):
    """Enumeration of supported task types."""

    QUERY = "query"
    ANALYSIS = "analysis"
    GENERATION = "generation"
    PROCESSING = "processing"
    CUSTOM = "custom"


class AgentExecutionRequest(BaseModel):
    """
    Request model for executing an agent task.

    This model defines the structure for requesting agent task execution
    with comprehensive validation and documentation.
    """

    query: str = Field(
        ...,
        description="The user's query or task to be processed by the agent",
        min_length=1,
        max_length=10000,
        example="What is the capital of France and what are its main attractions?",
    )

    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Optional metadata for the execution request including priority, timeout, and custom parameters",
        example={
            "priority": "high",
            "timeout": 120,
            "context": "educational",
            "format": "detailed",
        },
    )

    @validator("query")
    def validate_query(cls, v):
        """Validate query content."""
        if not v.strip():
            raise ValueError("Query cannot be empty or only whitespace")
        return v.strip()

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "query": "Analyze the current market trends for renewable energy stocks",
                "metadata": {
                    "priority": "high",
                    "timeout": 300,
                    "context": "financial_analysis",
                    "include_charts": True,
                    "data_sources": ["yahoo_finance", "bloomberg"],
                },
            }
        }


class AgentQueryRequest(BaseModel):
    """Request model for querying an agent directly."""

    query: str = Field(
        ...,
        description="The user's query to be processed by the agent",
        min_length=1,
        max_length=10000,
        example="What is the capital of France?",
    )

    user_id: Optional[str] = Field(
        None, description="ID of the user making the query", example="user_123"
    )

    organization_id: Optional[str] = Field(
        None, description="ID of the organization", example="org_456"
    )

    variables: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Variables to pass to the agent",
        example={"context": "business", "priority": "high"},
    )


class AgentQueryResponse(BaseModel):
    """Response model for agent query."""

    success: bool = Field(..., description="Whether the query was successful")

    message: str = Field(..., description="Status message")

    agent_response: Optional[Dict[str, Any]] = Field(
        None, description="The agent's response content"
    )

    run_id: str = Field(..., description="Unique identifier for this query execution")

    final: bool = Field(True, description="Whether this is the final response")


class ChatContext(BaseModel):
    """Chat context message."""

    role: str = Field(..., description="Role of the message sender", example="user")

    content: str = Field(
        ..., description="Content of the message", example="Hello, how can you help me?"
    )


class EnhancedChatRequest(BaseModel):
    """Enhanced request model for sending a chat message."""

    chat_context: List[ChatContext] = Field(
        ..., description="List of chat messages in the conversation context"
    )


class EnhancedChatResponse(BaseModel):
    """Enhanced response model for a chat message."""

    success: bool = Field(..., description="Whether the chat was successful")

    message: str = Field(..., description="Status message")

    agent_response: Optional[Dict[str, Any]] = Field(
        None, description="The agent's response content"
    )

    run_id: str = Field(..., description="Unique identifier for this chat execution")

    final: bool = Field(True, description="Whether this is the final response")


class AgentExecutionResponse(BaseModel):
    """Response model for agent execution."""

    task_id: str = Field(..., description="Unique identifier for the execution task")

    session_id: str = Field(..., description="Session identifier for the agent interaction")

    status: str = Field(..., description="Current status of the task", example="submitted")

    agent_id: str = Field(..., description="ID of the agent that processed the task")

    result: Optional[Dict[str, Any]] = Field(
        None, description="Task execution result (available when status is 'completed')"
    )

    error: Optional[str] = Field(None, description="Error message if the task failed")

    execution_time_ms: Optional[int] = Field(None, description="Execution time in milliseconds")


class AgentTaskStatus(BaseModel):
    """Model for checking agent execution status."""

    task_id: str = Field(..., description="Task identifier")

    status: str = Field(..., description="Current status of the task", example="running")

    progress: Optional[float] = Field(
        None, description="Task progress percentage (0-100)", ge=0, le=100
    )

    estimated_completion_time: Optional[int] = Field(
        None, description="Estimated completion time in seconds"
    )


class AgentExecutionError(BaseModel):
    """Model for agent execution errors."""

    error_code: str = Field(..., description="Error code identifier")

    error_message: str = Field(..., description="Human-readable error message")

    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")


class CreateSessionRequest(BaseModel):
    """
    Request model for creating a new agent session.

    A session represents a conversation context with an agent and is required
    for sending messages and executing tasks. If a conversation_id is provided,
    the session will be initialized with the existing conversation history.
    """

    conversation_id: str = Field(
        ...,
        description="conversation ID to initialize session with existing conversation history",
        min_length=1,
        max_length=100,
        example="conv_123e4567-e89b-12d3-a456-426614174000",
    )


class CreateSessionResponse(BaseModel):
    """
    Response model for successful session creation.

    Contains the unique session identifier that should be used for subsequent
    chat messages and task executions.
    """

    session_id: str = Field(
        ...,
        description="Unique identifier of the created session",
        example="session_123e4567-e89b-12d3-a456-426614174000",
    )


class ChatMessageRequest(BaseModel):
    """
    Request model for sending a chat message to an agent.

    The message will be processed by the agent within the context of the session.
    """

    message: str = Field(
        ...,
        description="The message content to send to the agent",
        min_length=1,
        max_length=10000,
        example="Hello! Can you help me analyze this data?",
    )


class ChatMessageResponse(BaseModel):
    """
    Response model for agent chat message.

    Contains the agent's response and optionally a task ID if the agent
    created a background task to process the request.
    """

    message: str = Field(
        ...,
        description="The agent's response message",
        example="I'd be happy to help you analyze your data. Please share the data you'd like me to review.",
    )

    task_id: Optional[str] = Field(
        None,
        description="ID of any background task created by the agent",
        example="task_123e4567-e89b-12d3-a456-426614174000",
    )


class QuickChatMessageResponse(BaseModel):
    """
    Response model for quick chat message response.

    Returns immediately with a processing ID while the actual message
    processing happens in the background.
    """

    success: bool = Field(
        True,
        description="Whether the message was successfully queued for processing",
        example=True,
    )

    message: str = Field(
        ...,
        description="Status message indicating the message was sent",
        example="Message sent successfully and is being processed",
    )

    processing_id: str = Field(
        ...,
        description="Unique identifier to track the message processing status",
        example="proc_123e4567-e89b-12d3-a456-426614174000",
    )

    session_id: str = Field(
        ...,
        description="The session ID for this chat message",
        example="session_123e4567-e89b-12d3-a456-426614174000",
    )


class MessageProcessingStatus(BaseModel):
    """
    Response model for message processing status.

    Contains the current status of a message being processed in the background.
    """

    processing_id: str = Field(
        ...,
        description="The processing ID to track",
        example="proc_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(
        ...,
        description="Current processing status",
        example="completed",
        # Possible values: "processing", "completed", "failed"
    )

    message: Optional[str] = Field(
        None,
        description="The agent's response message (available when status is completed)",
        example="I'd be happy to help you analyze your data.",
    )

    task_id: Optional[str] = Field(
        None,
        description="ID of any background task created by the agent",
        example="task_123e4567-e89b-12d3-a456-426614174000",
    )

    error: Optional[str] = Field(
        None,
        description="Error message if processing failed",
        example="Failed to process message: timeout",
    )

    created_at: Optional[str] = Field(
        None,
        description="When the processing started",
        example="2024-01-15T10:30:00Z",
    )

    completed_at: Optional[str] = Field(
        None,
        description="When the processing completed",
        example="2024-01-15T10:30:05Z",
    )


class ExecuteTaskRequest(BaseModel):
    """
    Request model for executing a specific task with an agent.

    Tasks are structured operations that agents can perform with specific
    inputs and expected outputs.
    """

    task_type: str = Field(
        ...,
        description="Type of task to execute",
        min_length=1,
        max_length=100,
        example="data_analysis",
    )

    task_input: Dict[str, Any] = Field(
        ...,
        description="Input parameters for the task",
        example={"data": [1, 2, 3, 4, 5], "analysis_type": "statistical_summary", "format": "json"},
    )


class ExecuteTaskResponse(BaseModel):
    """
    Response model for task execution.

    Contains the task identifier, current status, and results if completed.
    """

    task_id: str = Field(
        ...,
        description="Unique identifier of the executed task",
        example="task_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(..., description="Current status of the task", example="completed")

    result: Optional[Dict[str, Any]] = Field(
        None,
        description="Task execution result (available when status is 'completed')",
        example={"summary": {"mean": 3.0, "median": 3.0, "std": 1.58}, "analysis_complete": True},
    )


class RunAgentTaskRequest(BaseModel):
    """
    Request model for running a task directly with an agent.

    This is a simplified interface that doesn't require session management.
    """

    agent_id: str = Field(
        ...,
        description="Unique identifier of the agent to run the task",
        min_length=1,
        max_length=100,
        example="agent_123e4567-e89b-12d3-a456-426614174000",
    )

    task_type: str = Field(
        ...,
        description="Type of task to execute",
        min_length=1,
        max_length=100,
        example="text_generation",
    )

    task_input: Dict[str, Any] = Field(
        ...,
        description="Input parameters for the task",
        example={
            "prompt": "Write a summary of renewable energy benefits",
            "max_length": 500,
            "style": "professional",
        },
    )


class RunAgentTaskResponse(BaseModel):
    """
    Response model for direct task execution.

    Contains the task results and execution metadata.
    """

    task_id: str = Field(
        ...,
        description="Unique identifier of the executed task",
        example="task_123e4567-e89b-12d3-a456-426614174000",
    )

    status: str = Field(..., description="Final status of the task execution", example="completed")

    result: Optional[Dict[str, Any]] = Field(
        None,
        description="Task execution result and metadata",
        example={
            "generated_text": "Renewable energy offers numerous benefits...",
            "word_count": 487,
            "execution_time_ms": 1250,
        },
    )


class StreamingChatMessageRequest(BaseModel):
    """
    Request model for sending a streaming chat message to an agent.

    This model is used for chat messages that expect streaming responses,
    allowing real-time interaction with agents.
    """

    message: str = Field(
        ...,
        description="The message content to send to the agent for streaming response",
        min_length=1,
        max_length=10000,
        example="Can you explain machine learning concepts step by step?",
    )

    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Optional metadata for the streaming chat request",
        example={"stream_format": "sse", "include_timestamps": True, "context": "educational"},
    )


class StreamingChatChunk(BaseModel):
    """
    Model for individual chunks in a streaming chat response.

    Each chunk represents a piece of the agent's response as it's being generated.
    """

    chunk_id: Optional[str] = Field(
        None, description="Unique identifier for this chunk", example="chunk_001"
    )

    content: Optional[str] = Field(
        None,
        description="Text content of this chunk",
        example="Machine learning is a subset of artificial intelligence...",
    )

    chunk_type: str = Field(
        default="text", description="Type of content in this chunk", example="text"
    )

    is_final: bool = Field(
        default=False, description="Whether this is the final chunk in the stream"
    )

    metadata: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional metadata for this chunk",
        example={"timestamp": "2024-01-15T10:30:00Z", "token_count": 15, "confidence": 0.95},
    )


class DeleteSessionRequest(BaseModel):
    """
    Request model for deleting an agent session.

    This model defines the structure for requesting session deletion
    with optional parameters for force deletion and reason tracking.
    """

    reason: Optional[str] = Field(
        "user_request",
        description="Reason for session deletion",
        max_length=200,
        example="chat_complete",
    )

    force: bool = Field(
        False,
        description="Whether to force delete the session even if it doesn't exist",
        example=False,
    )

    @validator("reason")
    def validate_reason(cls, v):
        """Validate reason content."""
        if v and len(v.strip()) == 0:
            raise ValueError("Reason cannot be empty if provided")
        return v.strip() if v else "user_request"

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "reason": "chat_complete",
                "force": False,
            }
        }


class DeleteSessionResponse(BaseModel):
    """
    Response model for successful session deletion.

    Contains the deletion status, session identifier, and metadata about the deletion.
    """

    success: bool = Field(
        ...,
        description="Whether the session was successfully deleted",
        example=True,
    )

    session_id: str = Field(
        ...,
        description="Unique identifier of the deleted session",
        example="session_123e4567-e89b-12d3-a456-426614174000",
    )

    message: str = Field(
        ...,
        description="Status message describing the deletion result",
        example="Session deleted successfully",
    )

    deleted_at: Optional[str] = Field(
        None,
        description="ISO timestamp when the session was deleted",
        example="2024-01-15T10:30:00Z",
    )

    class Config:
        """Pydantic configuration."""

        schema_extra = {
            "example": {
                "success": True,
                "session_id": "session_123e4567-e89b-12d3-a456-426614174000",
                "message": "Session deleted successfully",
                "deleted_at": "2024-01-15T10:30:00Z",
            }
        }
