"""
Configuration module for the communication service.

This module provides the configuration settings for the communication service,
including environment variables and their default values.
"""

# Third-party imports
from pydantic_settings import BaseSettings


# Settings class
class Settings(BaseSettings):
    # Service configuration
    APP_NAME: str = "communication-service"

    # GRPC port for the communication service
    GRPC_PORT: str = "50055"

    # MongoDB configuration
    MONGO_HOST: str = "localhost"
    MONGO_PORT: str = "27017"
    MONGO_DB_NAME: str = "communication-service"
    MONGO_USERNAME: str = ""
    MONGO_PASSWORD: str = ""

    # Proto
    REPO_URL: str = ""
    GIT_TOKEN: str = ""
    ENV: str = "dev"

    # Debug mode
    DEBUG: bool = False

    # Class configuration
    class Config:
        env_file = ".env"
        case_sensitive = True


# Initialize settings
settings = Settings()
