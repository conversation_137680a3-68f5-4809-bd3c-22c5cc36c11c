"""
MongoDB model definition for conversations in the communication service.

This module defines the Conversation document model using mongoengine ODM.
It includes fields for tracking user-agent conversations with proper validation
and timestamp management.
"""

# Standard library imports
from datetime import datetime

# Third-party imports
import bson
from mongoengine import DateTime<PERSON>ield, Document, StringField
from mongoengine.errors import ValidationError

# Local imports
from app.grpc_ import communication_pb2


# Conversation model
class Conversation(Document):
    """
    MongoDB document model for conversations between users and agents.

    This model represents a conversation session between a user and an agent,
    storing metadata such as participants, channel information, and timestamps.

    Attributes:
        userId (str): Unique identifier for the user initiating the conversation.
        agentId (str): Unique identifier for the agent involved in the conversation.
        title (str): Title of the conversation.
        channel (str): Channel type for the conversation stored as string.
        createdAt (datetime): Timestamp for the creation of the conversation.
        updatedAt (datetime): Timestamp for the last update to the conversation.
    """

    # MongoDB collection & index configuration
    meta = {
        "collection": "conversations",
        "indexes": [
            # Single field indexes for the frequest queries
            "userId",
            "channel",
            # Compound indexes for time-based queries
            ("userId", "createdAt"),
        ],
        "ordering": ["-createdAt"],
    }

    # Id field for the conversation document
    id = StringField(primary_key=True, default=lambda: str(bson.ObjectId()))

    # User indentifier field with required validation
    userId = StringField(required=True)

    # Agent identifier field with required validation
    agentId = StringField(required=True)

    # Title field for the conversation
    title = StringField(required=True, min_length=1)

    # Channel field for the conversation (stored as string)
    channel = StringField(required=True)

    # Chat type field for the conversation
    chatType = StringField(required=True)

    # Summary field for the conversation
    summary = StringField(required=False)

    # Timestamp field for the creation of the conversation
    createdAt = DateTimeField(default=datetime.utcnow)

    # Timestamp field for the last update to the conversation
    updatedAt = DateTimeField(default=datetime.utcnow)

    # Add a validation method to ensure the channel value is valid

    def clean(self):
        """Update the updated timestamp and validate the channel before saving."""

        # If the creation timestamp is not set
        if not self.createdAt:
            # Set the creation timestamp to the current time
            self.createdAt = datetime.utcnow()

        # Update the updated timestamp
        self.updatedAt = datetime.utcnow()

        # Get channel enum names
        channel_names = [name for name in communication_pb2.ChannelType.keys()]

        # Get chat type enum names
        chat_type_names = [name for name in communication_pb2.ChatType.keys()]

        # If the channel value is not valid
        if self.channel not in channel_names:
            # Raise a validation error
            raise ValidationError(
                f"Invalid channel type: {self.channel}. Must be one of: {channel_names}"
            )

        # If the chat type value is not valid
        if self.chatType not in chat_type_names:
            # Raise a validation error
            raise ValidationError(
                f"Invalid chat type: {self.chatType}. Must be one of: {chat_type_names}"
            )

        # Call the parent clean method
        super().clean()

    # String representation of the conversation document
    def __str__(self):
        """Return the string representation of the conversation document."""

        # Return the string representation of the conversation document
        return f"Conversation({self.id}, {self.userId}, {self.channel}, {self.chatType}, {self.title[:16]}...)"

    # Convert the conversation document to a dictionary
    def to_dict(self):
        """Convert the conversation document to a dictionary."""

        # Return the conversation document as a dictionary
        return {
            "id": str(self.id),
            "userId": self.userId,
            "agentId": self.agentId,
            "title": self.title,
            "channel": self.channel,
            "chatType": self.chatType,
            "summary": self.summary,
            "createdAt": self.createdAt,
            "updatedAt": self.updatedAt,
        }

    # Convert the conversation document to a gRPC message
    def to_proto(self):
        """Convert the conversation document to a gRPC message."""

        # Create a new conversation message
        conversation = communication_pb2.Conversation()

        # Set the conversation ID
        conversation.id = str(self.id)

        # Set the user ID
        conversation.userId = self.userId

        # Set the agent ID
        conversation.agentId = self.agentId

        # Set the title
        conversation.title = self.title

        # Convert string channel to enum for protobuf
        if self.channel:
            conversation.channel = communication_pb2.ChannelType.Value(self.channel)

        # Convert string chat type to enum for protobuf
        if self.chatType:
            conversation.chatType = communication_pb2.ChatType.Value(self.chatType)

        # Set the summary if it exists
        if self.summary:
            conversation.summary = self.summary

        # Set the creation timestamp
        conversation.createdAt.FromDatetime(self.createdAt)

        # Set the update timestamp
        conversation.updatedAt.FromDatetime(self.updatedAt)

        # Return the conversation message
        return conversation
