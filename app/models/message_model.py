"""
MongoDB model definition for messages in the communication service.

This module defines the Message document model and related enums using mongoengine ODM.
It includes comprehensive validation for different message types and their associated
media content.
"""

# Standard library imports
from datetime import datetime

# Third-party imports
import bson
from google.protobuf import any_pb2
from mongoengine import Date<PERSON><PERSON><PERSON><PERSON>, Dict<PERSON>ield, Document, String<PERSON>ield
from mongoengine.errors import ValidationError

# Local imports
from app.grpc_ import communication_pb2


# Message model
class Message(Document):
    """
    MongoDB document model for messages in conversations.

    This model represents a message sent by a user or agent in a conversation,
    storing metadata such as sender information, message content, and timestamps.

    Attributes:
        conversationId (StringField): The ID of the conversation to which the message belongs.
        senderType (StringField): The type of the sender (user or agent).
        content (StringField): The content of the message.
        workflowId (StringField): The ID of the workflow associated with the message.
        workflowResponse (DictField): The response from the workflow.
        createdAt (DateTimeField): Timestamp for when the message was created.
        updatedAt (DateTimeField): Timestamp for when the message was last updated.
    """

    # MongoDB collection & index configuration
    meta = {
        "collection": "messages",
        "indexes": [
            # Single field indexes for the frequent queries
            "conversationId",
            "senderType",
            # Compound indexes for time-based queries
            ("conversationId", "createdAt"),
        ],
        "ordering": ["-createdAt"],
    }

    # Id field for the conversation document
    id = StringField(primary_key=True, default=lambda: str(bson.ObjectId()))

    # Conversation ID field
    conversationId = StringField(required=True)

    # Sender type field
    senderType = StringField(required=True)

    # Content field
    content = StringField(required=True)

    # Workflow ID field
    workflowId = StringField(null=True)

    # Workflow response field
    workflowResponse = DictField(default=None, null=True)

    # Timestamp field for the creation of the message
    createdAt = DateTimeField(default=datetime.utcnow)

    # Timestamp field for the last update to the message
    updatedAt = DateTimeField(default=datetime.utcnow)

    # Clean the message model
    def clean(self):
        """Update the updated timestamp before saving the message."""

        # If the creation timestamp is not set
        if not self.createdAt:
            # Set the creation timestamp to the current time
            self.createdAt = datetime.utcnow()

        # Update the updated timestamp
        self.updatedAt = datetime.utcnow()

        # Get enum names
        sender_type_names = [name for name, _ in communication_pb2.SenderType.items()]

        # Validate the sender type
        if self.senderType not in sender_type_names:
            # Raise a validation error
            raise ValidationError(
                f"Invalid sender type: {self.senderType}. Must be one of: {sender_type_names}"
            )

        # Call the parent clean method
        super().clean()

    # String representation of the message document
    def __str__(self):
        """Return the string representation of the message document."""

        # Return the string representation of the message document
        return f"Message({self.id}, {self.senderType}, {self.content[:16]}...)"

    # Convert the message document to a dictionary
    def to_dict(self):
        """Convert the message document to a dictionary."""

        # Return the message document as a dictionary
        return {
            "id": str(self.id),
            "conversationId": str(self.conversationId),
            "senderType": self.senderType,
            "content": self.content,
            "workflowId": self.workflowId,
            "workflowResponse": self.workflowResponse,
            "createdAt": self.createdAt,
            "updatedAt": self.updatedAt,
        }

    # Convert the message document to a gRPC message
    def to_proto(self):
        """Convert the message document to a gRPC message."""

        # Create a new message message
        message = communication_pb2.Message()

        # Set the message ID
        from app.utils.logger import setup_logger

        logger = setup_logger("communication-service/models/message_model.py")

        # Set the message ID
        try:
            message.id = str(self.id)
        except Exception as e:
            logger.error("Error setting id", value=repr(self.id), error=str(e))

        # Set the conversation ID
        try:
            message.conversationId = str(self.conversationId)
        except Exception as e:
            logger.error(
                "Error setting conversationId",
                value=repr(self.conversationId),
                error=str(e),
            )

        # Convert string enums to int for protobuf
        try:
            message.senderType = communication_pb2.SenderType.Value(self.senderType)
        except Exception as e:
            logger.warning(
                "Invalid senderType for protobuf enum, defaulting to SENDER_TYPE_UNSPECIFIED",
                senderType=str(self.senderType),
                error=str(e),
            )
            message.senderType = communication_pb2.SENDER_TYPE_UNSPECIFIED

        # Set the content of the message
        try:
            message.content = self.content if self.content is not None else ""
        except Exception as e:
            logger.error(
                "Error setting content", value=repr(self.content), error=str(e)
            )

        # Set the workflow ID
        try:
            message.workflowId = self.workflowId if self.workflowId is not None else ""
        except Exception as e:
            logger.error(
                "Error setting workflowId", value=repr(self.workflowId), error=str(e)
            )

        from datetime import datetime as dt

        # createdAt
        try:
            if isinstance(self.createdAt, dt):
                message.createdAt.FromDatetime(self.createdAt)
            elif self.createdAt is not None:
                try:
                    message.createdAt.FromDatetime(
                        dt.fromisoformat(str(self.createdAt))
                    )
                except Exception as e2:
                    logger.warning(
                        "Invalid createdAt, using current time",
                        createdAt=str(self.createdAt),
                        error=str(e2),
                    )
                    message.createdAt.FromDatetime(dt.utcnow())
            else:
                message.createdAt.FromDatetime(dt.utcnow())
        except Exception as e:
            logger.error(
                "Error setting createdAt", value=repr(self.createdAt), error=str(e)
            )

        # updatedAt
        try:
            if isinstance(self.updatedAt, dt):
                message.updatedAt.FromDatetime(self.updatedAt)
            elif self.updatedAt is not None:
                try:
                    message.updatedAt.FromDatetime(
                        dt.fromisoformat(str(self.updatedAt))
                    )
                except Exception as e2:
                    logger.warning(
                        "Invalid updatedAt, using current time",
                        updatedAt=str(self.updatedAt),
                        error=str(e2),
                    )
                    message.updatedAt.FromDatetime(dt.utcnow())
            else:
                message.updatedAt.FromDatetime(dt.utcnow())
        except Exception as e:
            logger.error(
                "Error setting updatedAt", value=repr(self.updatedAt), error=str(e)
            )

        # Set the workflowResponse map field robustly
        try:
            wf_resp = (
                self.workflowResponse if isinstance(self.workflowResponse, dict) else {}
            )
            from google.protobuf import struct_pb2, wrappers_pb2

            for key, value in wf_resp.items():
                any_value = any_pb2.Any()
                try:
                    # Pack protobuf messages directly
                    if hasattr(value, "DESCRIPTOR"):
                        any_value.Pack(value)
                        message.workflowResponse[key].CopyFrom(any_value)
                    # Pack string
                    elif isinstance(value, str):
                        wrapper = wrappers_pb2.StringValue(value=value)
                        any_value.Pack(wrapper)
                        message.workflowResponse[key].CopyFrom(any_value)
                    # Pack bool
                    elif isinstance(value, bool):
                        wrapper = wrappers_pb2.BoolValue(value=value)
                        any_value.Pack(wrapper)
                        message.workflowResponse[key].CopyFrom(any_value)
                    # Pack int
                    elif isinstance(value, int):
                        wrapper = wrappers_pb2.Int64Value(value=value)
                        any_value.Pack(wrapper)
                        message.workflowResponse[key].CopyFrom(any_value)
                    # Pack float
                    elif isinstance(value, float):
                        wrapper = wrappers_pb2.DoubleValue(value=value)
                        any_value.Pack(wrapper)
                        message.workflowResponse[key].CopyFrom(any_value)
                    # Pack dict as Struct
                    elif isinstance(value, dict):
                        struct = struct_pb2.Struct()
                        struct.update(value)
                        any_value.Pack(struct)
                        message.workflowResponse[key].CopyFrom(any_value)
                    # Pack list as ListValue
                    elif isinstance(value, list):
                        lv = struct_pb2.ListValue()
                        lv.extend(value)
                        any_value.Pack(lv)
                        message.workflowResponse[key].CopyFrom(any_value)
                    else:
                        logger.warning(
                            "workflowResponse value is not a supported type and was skipped",
                            key=key,
                            value_type=str(type(value)),
                        )
                except Exception as e:
                    logger.error(
                        "Failed to pack workflowResponse value",
                        key=key,
                        value_repr=repr(value),
                        value_type=str(type(value)),
                        error=str(e),
                    )
        except Exception as e:
            logger.error(
                "Error setting workflowResponse",
                value=repr(self.workflowResponse),
                error=str(e),
            )

        # Return the message message
        return message
