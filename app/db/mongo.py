"""
MongoDB connection and configuration module.

This module handles the MongoDB database connection setup and management
using mongoengine for the communication service.
"""

# Standard library imports
from typing import Optional

# Third-party imports
from mongoengine import connect, disconnect
from mongoengine.connection import get_db

# Local imports
from app.core.config import settings
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/db/mongo.py")


# Initialize the MongoDB connection
def init_db(
    host: str,
    port: int,
    database: str,
    username: Optional[str] = None,
    password: Optional[str] = None,
) -> None:
    """
    Initialize the MongoDB connection using mongoengine.

    This function establishes a connection to MongoDB using the provided
    credentials and configuration.

    Args:
        host: MongoDB server hostname
        port: MongoDB server port
        database: Name of the database to connect to
        username: Optional username for authentication
        password: Optional password for authentication

    Raises:
        ConnectionError: If unable to connect to MongoDB
    """
    try:
        # Construct authentication parameters if credentials are provided
        auth_kwargs = {}
        if username and password:
            auth_kwargs.update(
                {
                    "username": username,
                    "password": password,
                    "authentication_source": "admin",
                }
            )
            logger.info("Using authentication for MongoDB connection")
        else:
            logger.warning(
                "No authentication credentials provided for MongoDB connection"
            )

        # Connect to MongoDB
        if username and password:
            # Check if this is a MongoDB Atlas connection (hostname contains mongodb.net)
            if "mongodb.net" in host:
                # Use MongoDB Atlas connection string format
                uri = f"mongodb+srv://{username}:{password}@{host}/{database}?retryWrites=true&w=majority"
                logger.info("Using MongoDB Atlas connection format")

            else:
                # Use standard MongoDB URI connection string with authentication
                uri = f"mongodb://{username}:{password}@{host}:{port}/{database}?authSource=admin"

            connect(host=uri)
            logger.info("Connected to MongoDB using URI with authentication")
        else:
            # Connect without authentication
            connect(db=database, host=host, port=port)

        # Test connection by accessing database
        get_db()
        logger.info("Successfully connected to MongoDB")

    except Exception as e:
        # Log the error
        logger.error("Failed to connect to MongoDB: %s", str(e))

        # Raise an error to stop the application
        raise ConnectionError(f"Could not connect to MongoDB: {str(e)}")


def init_mongo_from_env() -> None:
    """
    Initialize MongoDB connection using environment variables.

    This function establishes the connection to MongoDB using configuration
    from environment variables.

    Environment Variables:
        MONGO_HOST (str): MongoDB host (default: "localhost")
        MONGO_PORT (str): MongoDB port (default: "27017")
        MONGO_DB (str): MongoDB database name (default: "communication-service")
        MONGO_USERNAME (str, optional): MongoDB username
        MONGO_PASSWORD (str, optional): MongoDB password

    Raises:
        RuntimeError: If unable to connect to MongoDB
    """
    try:
        # Get MongoDB configuration from environment
        host = settings.MONGO_HOST
        port = settings.MONGO_PORT
        database = settings.MONGO_DB_NAME
        username = settings.MONGO_USERNAME
        password = settings.MONGO_PASSWORD

        # Log MongoDB connection details (without password)
        logger.info(
            "Initializing MongoDB connection",
            host=host,
            port=port,
            database=database,
            username=username,
            auth_provided=bool(username and password),
        )

        # Initialize database connection
        init_db(
            host=host,
            port=port,
            database=database,
            username=username,
            password=password,
        )

    except Exception as e:
        # Log error
        logger.error("Failed to initialize MongoDB connection", error=str(e))

        # Raise error
        raise RuntimeError("Could not connect to MongoDB") from e


# Close the MongoDB connection
def close_db() -> None:
    """
    Close all MongoDB connections.

    This function should be called when shutting down the application
    to properly close all database connections.
    """
    try:
        # Disconnect from all databases
        disconnect()

        # Log the success
        logger.info("Successfully closed all MongoDB connections")
    except Exception as e:
        # Log the error
        logger.error("Error closing MongoDB connections: %s", str(e))

        # Raise an error to stop the application
        raise ConnectionError(f"Could not close MongoDB connections: {str(e)}")
