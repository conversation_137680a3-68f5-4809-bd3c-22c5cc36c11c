import grpc
from typing import Dict, List, Optional, Any
import httpx
from app.schemas.a2a import AgentCard
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)

# Import gRPC modules from main API gateway
try:
    import sys
    import os

    # Add the main API gateway path to sys.path to import gRPC modules
    main_api_gateway_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "api-gateway"
    )
    if main_api_gateway_path not in sys.path:
        sys.path.append(main_api_gateway_path)

    from app.grpc_ import agent_pb2, agent_pb2_grpc  # noqa: F401

    GRPC_AVAILABLE = True
except ImportError as e:
    logger.warning(f"gRPC modules not available: {e}. Using mock data only.")
    GRPC_AVAILABLE = False


class AgentServiceClient:
    """
    Client for interacting with the Agent Service via gRPC.

    This class provides methods for retrieving agent information,
    executing agents, and managing agent state using the main API
    gateway's gRPC service.
    """

    def __init__(self, base_url: str = None):
        """
        Initialize the Agent Service Client.

        Args:
            base_url: Base URL for the Agent Service API (fallback for HTTP)
        """
        self.base_url = base_url or settings.AGENT_SERVICE_URL
        self.client = httpx.AsyncClient(base_url=self.base_url, timeout=30.0)

        # Initialize gRPC client if available and enabled
        self.grpc_client = None
        grpc_enabled = getattr(settings, "AGENT_SERVICE_GRPC_ENABLED", False)

        if GRPC_AVAILABLE and grpc_enabled:
            try:
                # Use main API gateway's agent service configuration
                agent_service_host = getattr(settings, "AGENT_SERVICE_HOST", "localhost")
                agent_service_port = getattr(settings, "AGENT_SERVICE_PORT", "50057")

                grpc_address = f"{agent_service_host}:{agent_service_port}"
                self.grpc_channel = grpc.insecure_channel(grpc_address)
                self.grpc_client = agent_pb2_grpc.AgentServiceStub(self.grpc_channel)
                logger.info(f"gRPC client initialized for {grpc_address}")
            except Exception as e:
                logger.error(f"Failed to initialize gRPC client: {e}")
                logger.info("Falling back to HTTP-only mode")
                self.grpc_client = None
        elif not grpc_enabled:
            logger.info("gRPC client disabled by configuration, using mock data")
        else:
            logger.info("gRPC not available, using mock data")

        # Mock data for development/fallback
        self._initialize_mock_agents()

    def _initialize_mock_agents(self):
        """Initialize mock agents for development."""
        self.mock_agents = {
            "agent-1": AgentCard(
                name="Text Assistant",
                description="A general-purpose text assistant that can answer questions and provide information.",
                url="https://api.example.com/agents/text-assistant",
                version="1.0.0",
                defaultInputModes=["text"],
                defaultOutputModes=["text"],
                capabilities={
                    "text_chat": True,
                    "question_answering": True,
                    "streaming": True,
                },
                skills=[
                    {
                        "name": "question_answering",
                        "description": "Answer questions on various topics",
                        "parameters": {"max_tokens": 4096, "model": "gpt-4"},
                    }
                ],
            ),
            "agent-2": AgentCard(
                name="Code Generator",
                description="An agent specialized in generating and explaining code in various programming languages.",
                url="https://api.example.com/agents/code-generator",
                version="1.0.0",
                defaultInputModes=["text"],
                defaultOutputModes=["text"],
                capabilities={
                    "text_chat": True,
                    "code_generation": True,
                    "streaming": True,
                },
                skills=[
                    {
                        "name": "code_generation",
                        "description": "Generate code in various programming languages",
                        "parameters": {
                            "model": "claude-3-opus",
                            "supported_languages": ["python", "javascript", "java", "c++", "go"],
                        },
                    }
                ],
            ),
            "agent-3": AgentCard(
                name="Image Creator",
                description="An agent that can generate images based on text descriptions.",
                url="https://api.example.com/agents/image-creator",
                version="1.0.0",
                defaultInputModes=["text"],
                defaultOutputModes=["image"],
                capabilities={
                    "text_chat": True,
                    "image_generation": True,
                    "streaming": False,
                },
                skills=[
                    {
                        "name": "image_generation",
                        "description": "Generate images from text descriptions",
                        "parameters": {
                            "model": "stable-diffusion-xl",
                            "max_resolution": "1024x1024",
                        },
                    }
                ],
            ),
            "agent-4": AgentCard(
                name="Data Analyst",
                description="An agent specialized in analyzing data and generating insights.",
                url="https://api.example.com/agents/data-analyst",
                version="1.0.0",
                defaultInputModes=["text"],
                defaultOutputModes=["text"],
                capabilities={
                    "text_chat": True,
                    "data_analysis": True,
                    "streaming": True,
                },
                skills=[
                    {
                        "name": "data_analysis",
                        "description": "Analyze data and generate insights",
                        "parameters": {
                            "supported_formats": ["csv", "json", "excel"],
                            "max_file_size_mb": 10,
                        },
                    }
                ],
            ),
            "agent-5": AgentCard(
                name="Document Processor",
                description="An agent that can process and extract information from documents.",
                url="https://api.example.com/agents/document-processor",
                version="1.0.0",
                defaultInputModes=["text"],
                defaultOutputModes=["text"],
                capabilities={
                    "text_chat": True,
                    "document_processing": True,
                    "streaming": True,
                },
                skills=[
                    {
                        "name": "document_processing",
                        "description": "Process and extract information from documents",
                        "parameters": {
                            "supported_formats": ["pdf", "docx", "txt"],
                            "max_file_size_mb": 20,
                        },
                    }
                ],
            ),
        }

    def _convert_grpc_agent_to_card(self, grpc_agent) -> AgentCard:
        """Convert gRPC agent response to AgentCard."""
        try:
            # Extract capabilities from agent data
            capabilities = {
                "text_chat": True,
                "streaming": True,
            }

            if hasattr(grpc_agent, "agent_capabilities"):
                # Add more capabilities based on agent data
                capabilities["question_answering"] = True

            return AgentCard(
                id=grpc_agent.id,
                name=grpc_agent.name,
                description=grpc_agent.description or "",
                url=f"https://api.example.com/agents/{grpc_agent.id}",
                version="1.0.0",
                defaultInputModes=["text"],
                defaultOutputModes=["text"],
                capabilities=capabilities,
                skills=[
                    {
                        "name": "general_assistance",
                        "description": "General AI assistance",
                        "parameters": {
                            "model": grpc_agent.model_name or "",
                            "department": grpc_agent.department or "",
                            "category": grpc_agent.category or 0,
                        },
                    }
                ],
            )
        except Exception as e:
            logger.error(f"Error converting gRPC agent to card: {e}")
            return None

    async def get_all_agents(self) -> List[AgentCard]:
        """
        Get all available agents.

        Returns:
            List of agent cards
        """
        try:
            # Try gRPC first if available
            if self.grpc_client and GRPC_AVAILABLE:
                try:
                    request = agent_pb2.ListAgentsRequest(
                        page=1,
                        page_size=100,  # Get a large number of agents
                        is_a2a=True,  # Only get A2A-enabled agents
                    )
                    response = self.grpc_client.listAgents(request)

                    agent_cards = []
                    for grpc_agent in response.agents:
                        card = self._convert_grpc_agent_to_card(grpc_agent)
                        if card:
                            agent_cards.append(card)

                    logger.info(f"Retrieved {len(agent_cards)} agents via gRPC")
                    return agent_cards

                except grpc.RpcError as e:
                    logger.error(f"gRPC error getting agents: {e}")
                    # Fall back to mock data
                except Exception as e:
                    logger.error(f"Error getting agents via gRPC: {e}")
                    # Fall back to mock data

            # Fallback to mock data
            logger.info("Using mock agent data")
            return list(self.mock_agents.values())

        except Exception as e:
            logger.error(f"Error getting agents: {e}")
            return list(self.mock_agents.values())

    async def get_agent(self, agent_id: str) -> Optional[AgentCard]:
        """
        Get an agent by ID.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent card or None if not found
        """
        try:
            # Try gRPC first if available
            if self.grpc_client and GRPC_AVAILABLE:
                try:
                    request = agent_pb2.GetAgentRequest(id=agent_id)
                    response = self.grpc_client.getAgent(request)

                    if response.success and response.agent:
                        card = self._convert_grpc_agent_to_card(response.agent)
                        if card:
                            logger.info(f"Retrieved agent {agent_id} via gRPC")
                            return card

                except grpc.RpcError as e:
                    logger.error(f"gRPC error getting agent {agent_id}: {e}")
                    # Fall back to mock data
                except Exception as e:
                    logger.error(f"Error getting agent {agent_id} via gRPC: {e}")
                    # Fall back to mock data

            # Fallback to mock data
            return self.mock_agents.get(agent_id)

        except Exception as e:
            logger.error(f"Error getting agent {agent_id}: {e}")
            return self.mock_agents.get(agent_id)

    async def execute_agent(self, agent_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an agent with the given input.

        Note: This method is deprecated for A2A protocol usage.
        A2A protocol uses KafkaAgentExecutor directly for agent execution.

        Args:
            agent_id: ID of the agent to execute
            input_data: Input data for the agent

        Returns:
            Agent execution result

        Raises:
            Exception: If the agent execution fails
        """
        try:
            # This is a fallback implementation for non-A2A usage
            # A2A protocol uses KafkaAgentExecutor directly
            logger.warning(f"Using fallback agent execution for {agent_id}")
            return {
                "success": True,
                "message": "Agent executed via fallback method",
                "result": {
                    "output": f"Fallback response from agent {agent_id}",
                    "metadata": {
                        "execution_time": 0.1,
                        "method": "fallback",
                    },
                },
            }
        except Exception as e:
            logger.error(f"Error executing agent {agent_id}: {e}")
            raise

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
