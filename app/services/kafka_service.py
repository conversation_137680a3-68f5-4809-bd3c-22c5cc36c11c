"""
Kafka service for communicating with agents.

This module provides a service for sending and receiving messages to/from agents via Kafka.
"""

import asyncio
import json
import uuid
from typing import Any, Dict, List

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer  # type: ignore

from app.core.config import settings
from app.core.logging import get_logger

# Route to SSE manager for streaming
from app.helper.sse_manager import SseManager

from ..shared.constants import SSEEventType, ContentType

# Configure logging
logger = get_logger(__name__)

# Kafka topics
KAFKA_AGENT_CREATION_TOPIC = settings.KAFKA_AGENT_CREATION_TOPIC
KAFKA_AGENT_CHAT_TOPIC = settings.KAFKA_AGENT_CHAT_TOPIC
KAFKA_AGENT_RESPONSE_TOPIC = settings.KAFKA_AGENT_RESPONSE_TOPIC
KAFKA_AGENT_QUERY_TOPIC = settings.KAFKA_AGENT_QUERY_TOPIC
KAFKA_AGENT_SESSION_DELETION_TOPIC = settings.KAFKA_AGENT_SESSION_DELETION_TOPIC


class KafkaService:
    """
    Service for communicating with agents via Kafka.
    """

    def __init__(self):
        """
        Initialize the Kafka service.
        """
        self.producer = None
        self.consumer = None
        self._initialized = False
        self.bootstrap_servers = settings.KAFKA_BOOTSTRAP_SERVERS or "localhost:9092"
        self.response_handlers: Dict[str, asyncio.Future] = {}
        self.consumer_task = None
        self.sse_manager = SseManager()

    async def initialize(self):
        """
        Initialize the Kafka producer and consumer.
        """
        if self._initialized:
            return

        try:
            # Initialize producer
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                max_request_size=524288000,  # 500MB max request size
            )

            # Initialize consumer
            self.consumer = AIOKafkaConsumer(
                KAFKA_AGENT_RESPONSE_TOPIC,
                bootstrap_servers=self.bootstrap_servers,
                group_id=f"api-gateway-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )

            # Start producer and consumer
            await self.producer.start()
            await self.consumer.start()

            # Start consumer task
            self.consumer_task = asyncio.create_task(self._consume_messages())

            self._initialized = True
            logger.info("Kafka service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Kafka service: {e}")
            await self.cleanup()
            raise

    async def cleanup(self):
        """
        Clean up Kafka resources.
        """
        if self.consumer_task:
            self.consumer_task.cancel()
            try:
                await self.consumer_task
            except asyncio.CancelledError:
                pass

        if self.producer:
            await self.producer.stop()

        if self.consumer:
            await self.consumer.stop()

        self._initialized = False
        logger.info("Kafka service cleaned up")

    async def send_message(
        self,
        topic: str,
        message: Dict[str, Any],
        correlation_id: str = None,
        reply_topic: str = KAFKA_AGENT_RESPONSE_TOPIC,
    ) -> str:
        """
        Send a message to a Kafka topic.

        Args:
            topic: The Kafka topic to send the message to
            message: The message to send
            correlation_id: The correlation ID for the message (generated if not provided)
            reply_topic: The topic to receive replies on
            client_id: The client ID for streaming responses (optional)

        Returns:
            The correlation ID for the message
        """
        if not self._initialized:
            await self.initialize()

        # Generate correlation ID if not provided
        if not correlation_id:
            correlation_id = str(uuid.uuid4())

        # Add correlation ID to message
        message["run_id"] = correlation_id

        # Prepare headers
        headers = [
            ("correlationId", correlation_id.encode("utf-8")),
            ("reply-topic", reply_topic.encode("utf-8")),
        ]

        try:
            # Encode and send message
            value = json.dumps(message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic} with correlation ID {correlation_id}")
            return correlation_id
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def _consume_messages(self):
        """
        Consume messages from Kafka and route them to the appropriate handlers.
        """
        try:
            async for msg in self.consumer:
                try:
                    # Extract correlation ID and client ID from headers
                    correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k.decode("utf-8") if isinstance(k, bytes) else k: (
                                v.decode("utf-8") if isinstance(v, bytes) else v
                            )
                            for k, v in msg.headers
                        }
                        correlation_id = headers_dict.get("correlationId")

                    # Decode message
                    response = json.loads(msg.value.decode("utf-8"))

                    logger.info(
                        f"Received message with correlation ID {correlation_id}: {response}"
                    )

                    if response.get("event_type") == SSEEventType.SESSION_INITIALIZED.value:
                        self.sse_manager.send_session_initialized(
                            session_id=response["session_id"], client_id=response["session_id"]
                        )

                    if response.get("event_type") == SSEEventType.MESSAGE_STREAMING.value:
                        self.sse_manager.send_message_streaming(
                            content=response.get("agent_response").get("content"),
                            content_type=response.get("content_type", ContentType.TEXT_PLAIN.value),
                            message_type=response.get("agent_response").get("message_type"),
                            source=response.get("agent_response").get("source"),
                            client_id=response["session_id"],
                            
                        )

                    if response.get("event_type") == SSEEventType.MESSAGE_RESPONSE.value:
                        self.sse_manager.send_message_response(
                            content=response.get("agent_response"),
                            content_type=response.get("content_type", ContentType.TEXT_PLAIN.value),
                            client_id=response["session_id"],
                        )

                    # Also route to regular handler if exists (for backward compatibility)
                    if correlation_id and correlation_id in self.response_handlers:
                        future = self.response_handlers[correlation_id]
                        if not future.done():
                            if (
                                response.get("event_type") == SSEEventType.MESSAGE_RESPONSE.value
                                or response.get("event_type")
                                == SSEEventType.SESSION_INITIALIZED.value
                                or (
                                    response.get("agent_response")
                                    and response.get("agent_response").get("source") != "user"
                                    and response.get("agent_response").get("message_type") == "text"
                                )
                            ):
                                future.set_result(response)
                                logger.debug(
                                    f"Routed message to handler for correlation ID {correlation_id}"
                                )

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode message: {e}")
                except Exception as e:
                    logger.error(f"Error processing message: {e}")

        except asyncio.CancelledError:
            logger.info("Consumer task cancelled")
        except Exception as e:
            logger.error(f"Error in consumer task: {e}")

    async def wait_for_response(self, correlation_id: str, timeout: int = 30) -> Dict[str, Any]:
        """
        Wait for a response with the given correlation ID.

        Args:
            correlation_id: The correlation ID to wait for
            timeout: The timeout in seconds

        Returns:
            The response message
        """
        if not self._initialized:
            await self.initialize()

        # Create future for response
        future = asyncio.Future()
        self.response_handlers[correlation_id] = future

        try:
            # Wait for response with timeout
            response = await asyncio.wait_for(future, timeout=timeout)
            return response
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for response with correlation ID {correlation_id}")
            raise
        finally:
            # Clean up handler
            self.response_handlers.pop(correlation_id, None)

    async def create_agent_session(
        self, agent_id: str, user_id: str, conversation_messages: List[Dict[str, str]] = None
    ) -> str:
        """
        Create a new agent session with optional conversation context.

        Args:
            agent_id: The ID of the agent
            user_id: The ID of the user
            conversation_messages: Optional list of conversation messages to initialize the session

        Returns:
            The session ID
        """
        # Prepare creation request
        creation_request = {
            "agent_id": agent_id,
            "user_id": user_id,
            "communication_type": "single",
            "agent_group_id": None,
        }

        # Add conversation context if provided
        if conversation_messages:
            creation_request["conversation_context"] = conversation_messages

        # Send request and get correlation ID
        correlation_id = await self.send_message(KAFKA_AGENT_CREATION_TOPIC, creation_request)

        # Wait for response
        response = await self.wait_for_response(correlation_id, timeout=60)

        # Extract session ID
        session_id = response.get("session_id")
        if not session_id:
            raise ValueError("No session ID in response")

        logger.info(f"Agent session created with ID {session_id}")
        return session_id

    async def send_chat_message(self, session_id: str, message: str) -> Dict[str, Any]:
        """
        Send a chat message to an agent.

        Args:
            session_id: The session ID
            message: The message to send

        Returns:
            The agent's response
        """

        chat_request = {
            "session_id": session_id,
            "chat_context": [{"role": "user", "content": message}],
            "chat_response": "stream",
        }

        # Send request and get correlation ID
        correlation_id = await self.send_message(KAFKA_AGENT_CHAT_TOPIC, chat_request)

        self.sse_manager.send_message_stream_started(client_id=session_id)

        # Wait for response
        response = await self.wait_for_response(correlation_id)

        self.sse_manager.send_message_end(session_id=session_id, client_id=session_id)

        return response

    async def execute_agent_task(self, agent_id: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task with an agent.

        Args:
            agent_id: The ID of the agent
            task: The task to execute

        Returns:
            The task result
        """

        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare task request
        task_request = {
            "agent_id": agent_id,
            "query": task.get("query", task.get("input", {}).get("question", "")),
            "run_id": run_id,
            "user_id": task.get("user_id", "test_user"),
            "organization_id": task.get("organization_id"),
            "variables": task.get("variables", {}),
        }

        # Send request and get correlation ID
        await self.send_message(KAFKA_AGENT_QUERY_TOPIC, task_request)

        # Wait for response
        response = await self.wait_for_response(correlation_id=run_id, timeout=120)

        return response

    async def query_agent(
        self,
        agent_id: str,
        query: str,
        user_id: str = None,
        organization_id: str = None,
        variables: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Query an agent directly without session creation.

        Args:
            agent_id: The ID of the agent
            query: The query to send to the agent
            user_id: The ID of the user making the query
            organization_id: The ID of the organization
            variables: Variables to pass to the agent

        Returns:
            The agent's response
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare query request
        query_request = {
            "agent_id": agent_id,
            "query": query,
            "run_id": run_id,
            "user_id": user_id or "api_user",
            "organization_id": organization_id,
            "variables": variables or {},
        }

        # Send request and get correlation ID
        await self.send_message(KAFKA_AGENT_QUERY_TOPIC, query_request)

        # Wait for response
        response = await self.wait_for_response(correlation_id=run_id, timeout=60)

        return response

    async def delete_agent_session(
        self,
        session_id: str,
        user_id: str = None,
        reason: str = "user_request",
        force: bool = False,
    ) -> Dict[str, Any]:
        """
        Delete an agent session.

        Args:
            session_id: The ID of the session to delete
            user_id: The ID of the user requesting deletion
            reason: Reason for deletion
            force: Whether to force delete even if session doesn't exist

        Returns:
            The deletion response
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare deletion request
        deletion_request = {
            "session_id": session_id,
            "run_id": run_id,
            "user_id": user_id or "api_user",
            "reason": reason,
            "force": force,
        }

        # Send request and get correlation ID
        correlation_id = await self.send_message(
            KAFKA_AGENT_SESSION_DELETION_TOPIC, deletion_request
        )

        # Wait for response
        response = await self.wait_for_response(correlation_id, timeout=30)

        logger.info(
            f"Session deletion request processed for session {session_id}",
            extra={
                "session_id": session_id,
                "user_id": user_id,
                "reason": reason,
                "force": force,
                "success": response.get("success", False),
            },
        )

        return response


# Create a singleton instance
kafka_service = KafkaService()
