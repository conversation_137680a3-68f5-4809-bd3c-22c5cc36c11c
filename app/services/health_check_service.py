import time
from logging import Logger
from typing import Any, Dict

import grpc

from app.core.config import settings
from app.core.logging import get_logger

logger: Logger = get_logger(__name__)


class HealthService:
    """Service for checking the health of microservices."""

    @staticmethod
    async def check_service(service_name: str, host: str, port: int) -> Dict[str, Any]:
        """
        Generic method to check the health of a service.

        Args:
            service_name: Name of the service
            host: Host address of the service
            port: Port number of the service

        Returns:
            Dict[str, Any]: Health status of the service
        """
        start_time = time.time()
        try:
            # Try to establish a gRPC connection
            channel = grpc.insecure_channel(f"{host}:{port}")
            # Set a deadline for the connection attempt
            grpc.channel_ready_future(channel).result(timeout=2)

            response_time = round((time.time() - start_time) * 1000, 2)  # in ms

            return {
                "service": service_name,
                "status": "healthy",
                "host": host,
                "port": port,
                "response_time_ms": response_time,
            }
        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)  # in ms
            logger.error(
                f"Error checking {service_name} service health", error=str(e), host=host, port=port
            )

            return {
                "service": service_name,
                "status": "unhealthy",
                "host": host,
                "port": port,
                "error": str(e),
                "response_time_ms": response_time,
            }

    @staticmethod
    async def check_user_service() -> Dict[str, Any]:
        """Check the health of the User Service."""
        return await HealthService.check_service(
            "user", settings.USER_SERVICE_HOST, settings.USER_SERVICE_PORT
        )

    @staticmethod
    async def check_admin_service() -> Dict[str, Any]:
        """Check the health of the Admin Service."""
        return await HealthService.check_service(
            "admin", settings.ADMIN_SERVICE_HOST, settings.ADMIN_SERVICE_PORT
        )

    @staticmethod
    async def check_all_services() -> Dict[str, Any]:
        """
        Check the health of all microservices.

        Returns:
            Dict[str, Any]: Dictionary with overall status and individual service statuses
        """
        # Define all service check methods
        service_checks = [
            HealthService.check_user_service,
            HealthService.check_admin_service,
        ]

        # Run all checks
        services = [await check() for check in service_checks]

        # Determine overall status
        overall_status = "healthy"
        for service in services:
            if service["status"] == "unhealthy":
                overall_status = "unhealthy"
                break

        return {"status": overall_status, "services": services, "timestamp": time.time()}

    @staticmethod
    async def check_specific_service(service_name: str) -> Dict[str, Any]:
        """
        Check the health of a specific microservice.

        Args:
            service_name: Name of the service to check (user, admin, etc.)

        Returns:
            Dict[str, Any]: Health status of the specified service
        """
        service_checks = {
            "user": HealthService.check_user_service,
            "admin": HealthService.check_admin_service,
        }

        if service_name not in service_checks:
            return {"service": service_name, "status": "unknown", "error": "Service not found"}

        return await service_checks[service_name]()
