"""
Agent routes for the Developer API Gateway.

This module provides comprehensive routes for agent management including:
- Creating and managing agent sessions
- Sending chat messages to agents
- Executing tasks with agents
- Querying agents directly
- Monitoring task status

All routes follow RESTful conventions and include comprehensive documentation,
input validation, and error handling.
"""

import json
import queue
import uuid
from logging import Logger

from fastapi import APIRouter, Depends, HTTPException, Path, status
from fastapi.responses import StreamingResponse

from app.core.auth_guard import role_required
from app.core.logging import get_logger
from app.helper.sse_manager import SseManager
from app.schemas.agent import (
    AgentQueryRequest,
    AgentQueryResponse,
    AgentTaskStatus,
    ChatMessageRequest,
    ChatMessageResponse,
    CreateSessionRequest,
    CreateSessionResponse,
    DeleteSessionRequest,
    DeleteSessionResponse,
    EnhancedChatRequest,
    EnhancedChatResponse,
    ExecuteTaskRequest,
    ExecuteTaskResponse,
    MessageProcessingStatus,
    QuickChatMessageResponse,
    RunAgentTaskRequest,
    RunAgentTaskResponse,
)
from app.schemas.communication import ConversationResponse, MessageList, MessageResponse
from app.services.agent_service import AgentServiceClient
from app.services.background_tasks import background_task_manager
from app.services.communication_service import CommunicationServiceClient
from app.services.kafka_service import kafka_service
from app.shared.constants import SenderType
from app.utils.redis.redis_service import RedisService

# Get logger
logger: Logger = get_logger(__name__)


def get_request_id() -> str:
    """Generate a unique request ID for tracking."""
    return f"req_{uuid.uuid4().hex[:16]}"


async def get_session_data(session_id: str) -> dict:
    """
    Get session data from Redis.

    Args:
        session_id: The session ID

    Returns:
        Session data dictionary or None if not found
    """
    session_key = f"session:{session_id}"
    session_data = redis_service.get_data_from_redis(session_key, "data")
    return session_data


async def store_chat_messages(
    conversation_id: str, user_message: str, agent_response: str, user_id: str
) -> None:
    """
    Store user and agent messages in the communication service.

    Args:
        conversation_id: The conversation ID
        user_message: The user's message content
        agent_response: The agent's response content
        user_id: The user ID
    """
    try:
        # Store user message
        if user_message:
            await communication_service.create_message(
                conversationId=conversation_id,
                senderType=SenderType.SENDER_TYPE_USER,
                content=user_message,
                userId=user_id,
            )
            logger.info(
                "User message stored successfully",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "message_length": len(user_message),
                },
            )

        # Store agent response
        if agent_response:
            await communication_service.create_message(
                conversationId=conversation_id,
                senderType=SenderType.SENDER_TYPE_ASSISTANT,
                content=agent_response,
                userId=user_id,
            )
            logger.info(
                "Agent response stored successfully",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "response_length": len(agent_response),
                },
            )
    except Exception as e:
        logger.error(
            f"Error storing chat messages: {str(e)}",
            extra={
                "conversation_id": conversation_id,
                "user_id": user_id,
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )


# Create router with enhanced Swagger documentation
router = APIRouter(
    prefix="/agents",
    tags=["Agent Management"],
    responses={
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {
                "application/json": {"example": {"detail": "Invalid authentication credentials"}}
            },
        },
        403: {
            "description": "Forbidden - Insufficient permissions",
            "content": {
                "application/json": {
                    "example": {"detail": "Insufficient permissions for this operation"}
                }
            },
        },
        404: {
            "description": "Not Found - Resource not found",
            "content": {"application/json": {"example": {"detail": "Resource not found"}}},
        },
        422: {
            "description": "Unprocessable Entity - Invalid request data",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "field_name"],
                                "msg": "field required",
                                "type": "value_error.missing",
                            }
                        ]
                    }
                }
            },
        },
        429: {
            "description": "Too Many Requests - Rate limit exceeded",
            "content": {
                "application/json": {
                    "example": {"detail": "Rate limit exceeded. Please try again later."}
                }
            },
        },
        500: {
            "description": "Internal Server Error",
            "content": {
                "application/json": {"example": {"detail": "Internal server error occurred"}}
            },
        },
    },
)

# Create agent service client
agent_service = AgentServiceClient()

# Create communication service client
communication_service = CommunicationServiceClient()

# Create Redis service for session management
redis_service = RedisService()

# Create SSE manager for streaming
sse_manager = SseManager()

# =============================================================================
# ROUTE DEFINITIONS
# =============================================================================


@router.post("/sessions", response_model=CreateSessionResponse)
async def create_session(
    request: CreateSessionRequest,
    current_user: dict = Depends(role_required(["user"])),
) -> CreateSessionResponse:
    """
    Create a new agent session for conversation and task execution.

    This endpoint creates a new session with the specified agent, which serves as a
    conversation context for subsequent chat messages and task executions. Each session
    maintains its own conversation history and state.

    ## Request Body
    - **conversation_id**: conversation ID to initialize session with existing conversation history

    ## Response
    Returns a session identifier that should be used for subsequent interactions.

    ## Example
    ```
    POST /api/v1/agents/sessions
    {
        "conversation_id": "conv_123e4567-e89b-12d3-a456-************"
    }
    ```

    ## Conversation Integration
    When a conversation_id is provided:
    - The system fetches the existing conversation data and messages
    - All previous messages are loaded as conversation context for the agent
    - The agent_id from the conversation is used if it differs from the request
    - The session is initialized with the full conversation history

    ## Errors
    - 400: Invalid request (missing agent_id, invalid format)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent not found
    - 500: Server error
    """

    try:

        print("current_user", current_user)

        # Use the authenticated user's ID if not specified
        user_id = current_user.get("user_id")
        # user_id = "fce79072-a235-4127-ac5b-b5b1709a8077"
        conversation_id = request.conversation_id

        # Log the request
        logger.info(
            "Creating agent session",
            extra={
                "user_id": user_id,
                "conversation_id": conversation_id,
                "operation": "create_session",
            },
        )

        # Initialize conversation context
        conversation_messages = []

        # Get conversation details
        conversation_response = await communication_service.get_conversation(
            conversation_id, user_id
        )

        conversation_data = ConversationResponse(**conversation_response)

        print("conversation_response", conversation_data)

        if not conversation_data.id:
            raise HTTPException(status_code=404, detail="Conversation not found")

        # Extract agent_id from conversation if not provided or validate it matches
        agent_id = conversation_data.agentId
        if not agent_id:
            logger.error(
                "Agent ID mismatch between request and conversation",
                extra={
                    "request_agent_id": agent_id,
                    "conversation_id": conversation_id,
                },
            )

        # Get conversation messages
        messages_response_raw = await communication_service.list_messages(
            conversation_id, user_id, page=1, limit=20
        )

        messages_response = MessageList(
            data=[MessageResponse(**msg) for msg in messages_response_raw["data"]],
            metadata=messages_response_raw["metadata"],
        )

        print("messages_response", messages_response)

        if messages_response.data:
            messages_data = messages_response.data

            # Convert messages to chat context format
            for msg in messages_data:
                sender_type = msg.senderType
                content = msg.content

                if content:  # Only include messages with content
                    role = "user" if sender_type == "USER" else "assistant"
                    conversation_messages.append({"role": role, "content": content})

            logger.info(
                "Loaded conversation messages for session creation",
                extra={
                    "conversation_id": conversation_id,
                    "message_count": len(conversation_messages),
                    "operation": "create_session",
                },
            )

        # Create session via Kafka with conversation context
        session_id = await kafka_service.create_agent_session(
            agent_id, user_id, conversation_messages
        )

        # Store session-conversation mapping in Redis for quick lookup
        session_key = f"session:{session_id}"
        session_data = {
            "conversation_id": conversation_id,
            "user_id": user_id,
            "agent_id": agent_id,
        }
        redis_service.set_data_to_redis(session_key, "data", session_data, expiry=86400)  # 24 hours

        # Log the session creation
        logger.info(
            "Agent session created successfully",
            extra={
                "user_id": user_id,
                "agent_id": agent_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "operation": "create_session",
            },
        )

        return CreateSessionResponse(session_id=session_id)
    except Exception as e:
        # Log the error
        logger.error(
            f"Error creating agent session: {str(e)}",
            extra={
                "user_id": user_id if "user_id" in locals() else None,
                "operation": "create_session",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")


@router.delete("/sessions/{session_id}", response_model=DeleteSessionResponse)
async def delete_session(
    request: DeleteSessionRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session to delete",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Delete an agent session and clean up associated resources.

    This endpoint allows you to delete an existing agent session, which will
    terminate the conversation context and clean up any associated resources.
    Once deleted, the session cannot be used for further interactions.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to delete

    ## Request Body
    - **reason**: Optional reason for deletion (e.g., "chat_complete", "user_request")
    - **force**: Whether to force delete even if the session doesn't exist

    ## Response
    Returns confirmation of deletion with session details and timestamp.

    ## Example
    ```
    DELETE /api/v1/agents/sessions/session_123
    {
        "reason": "chat_complete",
        "force": false
    }
    ```

    ## Errors
    - 400: Invalid request (invalid sessionn ID format)
    - 401: Unauthorised (invalid or missingng authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found (unless force=true)
    - 500: Server error
    """
    # Generate a request ID
    request_id = get_request_id()

    try:
        # Use the authenticated user's ID if available
        user_id = current_user.get("user_id", current_user.get("id"))

        # Log the request
        logger.info(
            "Deleting agent session",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "reason": request.reason,
                "force": request.force,
                "operation": "delete_session",
            },
        )

        # Delete session via Kafka
        response = await kafka_service.delete_agent_session(
            session_id=session_id,
            user_id=user_id,
            reason=request.reason,
            force=request.force,
        )

        # Extract response data
        success = response.get("success", False)
        message = response.get("message", "Session deletion processed")
        deleted_at = response.get("deleted_at")

        # Log the session deletion
        logger.info(
            "Agent session deletion processed",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "session_id": session_id,
                "success": success,
                "deleted_at": deleted_at,
                "operation": "delete_session",
            },
        )

        return DeleteSessionResponse(
            success=success,
            session_id=session_id,
            message=message,
            deleted_at=deleted_at,
        )

    except Exception as e:
        # Log the error
        logger.error(
            f"Error deleting agent session: {str(e)}",
            extra={
                "request_id": request_id,
                "user_id": user_id if "user_id" in locals() else None,
                "session_id": session_id,
                "operation": "delete_session",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to delete session: {str(e)}")


@router.post("/sessions/{session_id}/chat", response_model=ChatMessageResponse)
async def send_chat_message(
    request: ChatMessageRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Send a chat message to an agent within an existing session.

    This endpoint allows you to send a message to an agent within the context of an
    existing session. The agent will process the message and return a response. If the
    agent needs to perform background tasks, a task ID will be included in the response.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to send the message to

    ## Request Body
    - **message**: The message content to send to the agent (1-10,000 characters)

    ## Response
    Returns the agent's response message and optionally a task ID for background processing.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat
    {
        "message": "Can you analyze the sales data from last quarter?"
    }
    ```

    ## Errors
    - 400: Invalid request (empty message, message too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """

    try:
        # Get session data to retrieve conversation_id
        session_data = await get_session_data(session_id)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found or expired")

        conversation_id = session_data.get("conversation_id")
        user_id = session_data.get("user_id")

        # Send message via Kafka
        response = await kafka_service.send_chat_message(session_id, request.message)

        # Extract message from response
        agent_response = response.get("agent_response", {})
        content = agent_response.get("content", "")

        # Check if a task was created
        task_id = response.get("run_id")

        # Store conversation messages in communication service
        await store_chat_messages(
            conversation_id=conversation_id,
            user_message=request.message,
            agent_response=content,
            user_id=user_id,
        )

        logger.info(
            "Chat message processed and stored successfully",
            extra={
                "session_id": session_id,
                "conversation_id": conversation_id,
                "user_id": user_id,
                "task_id": task_id,
                "operation": "send_chat_message",
            },
        )

        return ChatMessageResponse(message=content, task_id=task_id)
    except Exception as e:

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to send chat message: {str(e)}")


@router.post("/sessions/{session_id}/chat/quick", response_model=QuickChatMessageResponse)
async def send_quick_chat_message(
    request: ChatMessageRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Send a chat message to an agent with quick response and background processing.

    This endpoint provides a quick response by immediately returning a processing ID
    while the actual message processing happens in the background. This is ideal for
    scenarios where you need to provide immediate feedback to users while the agent
    processes the message asynchronously.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to send the message to

    ## Request Body
    - **message**: The message content to send to the agent (1-10,000 characters)

    ## Response
    Returns immediately with a processing ID that can be used to check the status
    and retrieve the agent's response when processing is complete.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat/quick
    {
        "message": "Can you analyze the sales data from last quarter?"
    }
    ```

    ## Background Processing
    The message is processed asynchronously in the background:
    1. Message is queued for processing
    2. Agent processes the message via Kafka
    3. Response is stored in communication service
    4. Status is updated and can be checked via processing ID

    ## Errors
    - 400: Invalid request (empty message, message too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """

    try:
        # Get session data to retrieve conversation_id
        session_data = await get_session_data(session_id)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found or expired")

        conversation_id = session_data.get("conversation_id")
        user_id = session_data.get("user_id")

        # Start background processing
        processing_id = await background_task_manager.start_chat_message_processing(
            session_id=session_id,
            message=request.message,
            conversation_id=conversation_id,
            user_id=user_id,
        )

        logger.info(
            "Quick chat message queued for background processing",
            extra={
                "processing_id": processing_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "user_id": user_id,
                "operation": "send_quick_chat_message",
            },
        )

        return QuickChatMessageResponse(
            success=True,
            message="Message sent successfully and is being processed",
            processing_id=processing_id,
            session_id=session_id,
        )

    except Exception as e:
        logger.error(
            f"Error queuing quick chat message: {str(e)}",
            extra={
                "session_id": session_id,
                "error": str(e),
                "error_type": type(e).__name__,
                "operation": "send_quick_chat_message",
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to queue chat message: {str(e)}")


@router.get("/processing/{processing_id}/status", response_model=MessageProcessingStatus)
async def get_message_processing_status(
    processing_id: str = Path(
        ...,
        description="Unique identifier of the processing to check",
        example="proc_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Get the current status of a message being processed in the background.

    This endpoint allows you to check the status of a message that was submitted
    for background processing via the quick chat endpoint. It returns the current
    processing status and the agent's response when available.

    ## Path Parameters
    - **processing_id**: The unique identifier returned from the quick chat endpoint

    ## Response
    Returns the current processing status with the following possible states:
    - **processing**: Message is currently being processed
    - **completed**: Processing completed successfully with agent response
    - **failed**: Processing failed with error details

    ## Example
    ```
    GET /api/v1/agents/processing/proc_123/status
    ```

    ## Errors
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Processing ID not found
    - 500: Server error
    """

    try:
        # Get processing status
        status_data = await background_task_manager.get_processing_status(processing_id)

        if not status_data:
            raise HTTPException(status_code=404, detail="Processing ID not found")

        logger.info(
            "Processing status retrieved",
            extra={
                "processing_id": processing_id,
                "status": status_data.get("status"),
                "operation": "get_message_processing_status",
            },
        )

        return MessageProcessingStatus(**status_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error retrieving processing status: {str(e)}",
            extra={
                "processing_id": processing_id,
                "error": str(e),
                "error_type": type(e).__name__,
                "operation": "get_message_processing_status",
            },
        )

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to get processing status: {str(e)}")


@router.get("/sessions/{session_id}/chat/stream")
async def send_streaming_chat_message(
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    # current_user: dict = Depends(role_required(["user"])),
):
    """
    Send a chat message to an agent with streaming response.

    This endpoint allows you to send a message to an agent and receive a streaming
    response in real-time. The response is delivered as Server-Sent Events (SSE),
    allowing for real-time interaction with the agent as it generates its response.

    ## Path Parameters
    - **session_id**: The unique identifier of the session to send the message to

    ## Request Body
    - **message**: The message content to send to the agent (1-10,000 characters)
    - **metadata**: Optional metadata for the streaming request

    ## Response
    Returns a streaming response using Server-Sent Events (SSE) format.
    Each event contains a JSON object with chunk data.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat/stream
    {
        "message": "Can you explain machine learning step by step?",
        "metadata": {
            "stream_format": "sse",
            "include_timestamps": true
        }
    }
    ```

    ## Stream Format
    The response stream follows the SSE format:
    ```
    data: {"content": "Machine learning is...", "chunk_type": "text", "is_final": false}
    data: {"content": " a subset of AI...", "chunk_type": "text", "is_final": false}
    data: [DONE]
    ```

    ## Errors
    - 400: Invalid request (empty message, message too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """

    try:

        # Create a queue for this client to receive SSE events
        client_queue = queue.Queue()
        client_id = session_id

        def event_stream():
            """
            Generator function to stream events to the client.
            """
            try:
                # Add the client to the SSE manager
                sse_manager.add_client(client_id, client_queue)

                while not sse_manager.is_shutting_down:
                    try:
                        # Wait for an event from the queue
                        event = client_queue.get(timeout=10)
                        # Only process events meant for this client or broadcast events
                        if event.get("client_id") is None or event.get("client_id") == client_id:
                            yield (
                                f"event: {event['event']}\ndata: {json.dumps(event['data'])}"
                                f"\ntype: {event['type']}\nid: {event['timestamp']}\n\n"
                            )
                    except queue.Empty:
                        # Send a keep-alive event if no events are received
                        yield "event: keep-alive\ndata: keep-alive\n\n"

            except GeneratorExit:
                # Handle client disconnection
                logger.info(f"Client {client_id} connection closed")
            finally:
                # Remove the client from the SSE manager
                sse_manager.remove_client(client_id)

        # Return a streaming response with the event stream
        return StreamingResponse(
            event_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # Disable nginx buffering
            },
        )

    except HTTPException:
        # Clean up on HTTP exceptions
        sse_manager.remove_client(client_id)
        raise
    except Exception as e:
        # Clean up on other exceptions
        sse_manager.remove_client(client_id)

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start streaming chat: {str(e)}",
        )


@router.post("/{agent_id}/sessions/{session_id}/tasks", response_model=ExecuteTaskResponse)
async def execute_task(
    request: ExecuteTaskRequest,
    agent_id: str = Path(
        ...,
        description="Unique identifier of the agent",
        example="agent_123e4567-e89b-12d3-a456-************",
    ),
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
) -> ExecuteTaskResponse:
    """
    Execute a specific task with an agent within an existing session.

    This endpoint allows you to execute structured tasks with an agent. Tasks are
    specific operations that agents can perform, such as data analysis, content
    generation, or complex processing workflows. The task is executed within the
    context of the session.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to execute the task
    - **session_id**: The unique identifier of the session context

    ## Request Body
    - **task_type**: The type of task to execute (e.g., "data_analysis", "text_generation")
    - **task_input**: Input parameters and data for the task

    ## Response
    Returns the task execution result with status and output data.

    ## Example
    ```
    POST /api/v1/agents/agent_123/sessions/session_456/tasks
    {
        "task_type": "data_analysis",
        "task_input": {
            "data": [1, 2, 3, 4, 5],
            "analysis_type": "statistical_summary"
        }
    }
    ```

    ## Errors
    - 400: Invalid request (missing task_type, invalid task_input)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent or session not found
    - 422: Invalid task parameters
    - 500: Server error
    """
    # Generate a request ID
    request_id = get_request_id()

    try:
        # Get user ID
        user_id = current_user.get("id")

        # Prepare task
        task = {
            "type": request.task_type,
            "input": request.task_input,
            "user_id": user_id,  # Add user ID to task
        }

        # Execute task via Kafka
        response = await kafka_service.execute_agent_task(agent_id, session_id, task)

        # Extract result from response
        task_id = response.get("task_id", str(uuid.uuid4()))
        status = response.get("status", {}).get("state", "completed")
        result = response.get("result")

        return ExecuteTaskResponse(
            task_id=task_id,
            status=status,
            result=result,
        )
    except Exception as e:

        # Raise HTTP exception
        raise HTTPException(status_code=500, detail=f"Failed to execute task: {str(e)}")


@router.post("/{agent_id}/run-task", response_model=RunAgentTaskResponse)
async def run_agent_task(
    request: RunAgentTaskRequest,
    agent_id: str = Path(
        ...,
        description="Unique identifier of the agent",
        example="agent_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Run a task directly with an agent without session management.

    This endpoint provides a simplified interface for executing tasks with an agent
    without the need to create and manage sessions. It's ideal for stateless operations
    where you don't need conversation context or session persistence.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to run the task

    ## Request Body
    - **agent_id**: The agent identifier (must match path parameter)
    - **task_type**: The type of task to execute
    - **task_input**: Input parameters and data for the task

    ## Response
    Returns the task execution result with status and output data.

    ## Example
    ```
    POST /api/v1/agents/agent_123/run-task
    {
        "agent_id": "agent_123e4567-e89b-12d3-a456-************",
        "task_type": "text_generation",
        "task_input": {
            "prompt": "Write a summary of renewable energy benefits",
            "max_length": 500
        }
    }
    ```

    ## Errors
    - 400: Invalid request (missing task_type, invalid task_input)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent not found
    - 422: Invalid task parameters
    - 500: Server error
    """
    request_id = get_request_id()

    try:
        user_id = current_user.get("user_id", current_user.get("id"))

        task = {
            "type": request.task_type,
            "input": request.task_input,
            "user_id": user_id,
        }

        # Send task request via Kafka and receive chat response
        response = await kafka_service.execute_agent_task(agent_id, task)

        task_id = response.get("task_id", str(uuid.uuid4()))
        status = response.get("status", {}).get("state", "completed")
        result = response.get("result")

        return RunAgentTaskResponse(
            task_id=task_id,
            status=status,
            result=result,
        )
    except Exception as e:

        raise HTTPException(status_code=500, detail=f"Failed to run agent task: {str(e)}")


@router.get("/tasks/{task_id}/status", response_model=AgentTaskStatus)
async def get_task_status(
    task_id: str = Path(
        ...,
        description="Unique identifier of the task",
        example="task_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Get the current status and progress of an agent execution task.

    This endpoint allows you to check the status of a previously submitted task
    using the task ID returned from task execution endpoints. It provides real-time
    status updates and progress information.

    ## Path Parameters
    - **task_id**: The unique identifier of the task to check

    ## Response
    Returns the current task status, progress percentage, and estimated completion time.

    ## Status Values
    - **submitted**: Task has been submitted and is queued for processing
    - **running**: Task is currently being processed by the agent
    - **completed**: Task has completed successfully
    - **failed**: Task has failed with an error
    - **cancelled**: Task was cancelled by the user

    ## Example
    ```
    GET /api/v1/agents/tasks/task_123/status
    ```

    ## Errors
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Task not found or access denied
    - 500: Server error
    """
    request_id = get_request_id()
    user_id = current_user.get("id")

    try:
        logger.info(
            "Task status request received",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "task_id": task_id,
                "operation": "get_task_status",
            },
        )

        # TODO: Implement actual task status retrieval
        # This would typically involve querying a task status store or cache
        # For now, return a mock response

        # In a real implementation, you would:
        # 1. Query the task status from Redis/database
        # 2. Check if the user has permission to view this task
        # 3. Return the actual status and progress

        response = AgentTaskStatus(
            task_id=task_id,
            status="completed",  # Mock status
            progress=100.0,
            estimated_completion_time=None,
        )

        logger.info(
            "Task status retrieved successfully",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "task_id": task_id,
                "status": response.status,
                "operation": "get_task_status",
            },
        )

        return response

    except Exception as e:
        logger.error(
            "Failed to retrieve task status",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "task_id": task_id,
                "error": str(e),
                "operation": "get_task_status",
            },
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve task status: {str(e)}",
        )


@router.post("/{agent_id}/query", response_model=AgentQueryResponse)
async def query_agent(
    request: AgentQueryRequest,
    agent_id: str = Path(
        ...,
        description="Unique identifier of the agent to query",
        example="agent_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Query an agent directly without creating a session.

    This endpoint allows you to send a query directly to an agent and receive a response
    without the need to create and manage a session. It's ideal for simple, one-off queries
    that don't require maintaining conversation state or context.

    ## Path Parameters
    - **agent_id**: The unique identifier of the agent to query

    ## Request Body
    - **query**: The query text to send to the agent (1-10,000 characters)
    - **user_id**: Optional user ID (defaults to authenticated user if not provided)
    - **organization_id**: Optional organization context
    - **variables**: Optional key-value pairs to pass to the agent

    ## Response
    Returns the agent's response with execution metadata including success status and run ID.

    ## Example
    ```
    POST /api/v1/agents/agent_123/query
    {
        "query": "What are the benefits of renewable energy?",
        "variables": {
            "context": "business",
            "format": "bullet_points"
        }
    }
    ```

    ## Errors
    - 400: Invalid request (empty query, query too long)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Agent not found
    - 422: Invalid query parameters
    - 500: Server error
    """
    request_id = get_request_id()
    user_id = current_user.get("id")

    try:
        # Use the authenticated user's ID if not specified in request
        query_user_id = request.user_id or user_id

        logger.info(
            "Agent query request received",
            extra={
                "request_id": request_id,
                "user_id": query_user_id,
                "agent_id": agent_id,
                "query_length": len(request.query),
                "operation": "query_agent",
            },
        )

        # Send query via Kafka
        response = await kafka_service.query_agent(
            agent_id=agent_id,
            query=request.query,
            user_id=query_user_id,
            organization_id=request.organization_id,
            variables=request.variables,
        )

        # Extract response data
        success = response.get("success", False)
        message = response.get("message", "Query processed")
        agent_response = response.get("agent_response")
        run_id = response.get("run_id", request_id)
        final = response.get("final", True)

        logger.info(
            "Agent query processed successfully",
            extra={
                "request_id": request_id,
                "user_id": query_user_id,
                "agent_id": agent_id,
                "run_id": run_id,
                "success": success,
                "operation": "query_agent",
            },
        )

        return AgentQueryResponse(
            success=success,
            message=message,
            agent_response=agent_response,
            run_id=run_id,
            final=final,
        )

    except Exception as e:
        logger.error(
            "Failed to process agent query",
            extra={
                "request_id": request_id,
                "user_id": user_id,
                "agent_id": agent_id,
                "error": str(e),
                "operation": "query_agent",
            },
        )

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to process agent query: {str(e)}",
        )


@router.post("/sessions/{session_id}/chat-enhanced", response_model=EnhancedChatResponse)
async def send_enhanced_chat_message(
    request: EnhancedChatRequest,
    session_id: str = Path(
        ...,
        description="Unique identifier of the session",
        example="session_123e4567-e89b-12d3-a456-************",
    ),
    current_user: dict = Depends(role_required(["user"])),
):
    """
    Send an enhanced chat message with full conversation context to an agent.

    This endpoint allows you to send a message to an agent along with the full
    conversation context. It's useful for maintaining conversation continuity
    and providing the agent with complete context for better responses.

    ## Path Parameters
    - **session_id**: The unique identifier of the session

    ## Request Body
    - **chat_context**: Array of conversation messages with role and content
    - **user_id**: Optional user ID (defaults to authenticated user)
    - **organization_id**: Optional organization context

    ## Response
    Returns enhanced response with success status, message, and run metadata.

    ## Example
    ```
    POST /api/v1/agents/sessions/session_123/chat-enhanced
    {
        "chat_context": [
            {"role": "user", "content": "What is machine learning?"},
            {"role": "assistant", "content": "Machine learning is..."},
            {"role": "user", "content": "Can you give me examples?"}
        ]
    }
    ```

    ## Errors
    - 400: Invalid request (empty context, invalid message format)
    - 401: Unauthorized (invalid or missing authentication)
    - 403: Forbidden (insufficient permissions)
    - 404: Session not found or expired
    - 500: Server error
    """
    request_id = get_request_id()

    try:
        # Get session data to retrieve conversation_id
        session_data = await get_session_data(session_id)
        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found or expired")

        conversation_id = session_data.get("conversation_id")
        user_id = session_data.get("user_id")

        logger.info(
            "Sending enhanced chat message",
            extra={
                "request_id": request_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "message_count": len(request.chat_context),
                "operation": "send_enhanced_chat_message",
            },
        )

        # Prepare chat request for Kafka
        chat_request = {
            "session_id": session_id,
            "chat_context": [
                {"role": msg.role, "content": msg.content} for msg in request.chat_context
            ],
        }

        # Send via Kafka using the existing method but with enhanced structure
        from app.services.kafka_service import KAFKA_AGENT_CHAT_TOPIC

        correlation_id = await kafka_service.send_message(KAFKA_AGENT_CHAT_TOPIC, chat_request)

        # Wait for response
        response = await kafka_service.wait_for_response(correlation_id)

        # Extract response data
        success = response.get("success", True)
        message = response.get("message", "Chat message processed")
        agent_response = response.get("agent_response")
        run_id = response.get("run_id", correlation_id)
        final = response.get("final", True)

        # Extract the last user message and agent response for storage
        if request.chat_context:
            last_user_message = None
            for msg in reversed(request.chat_context):
                if msg.role == "user":
                    last_user_message = msg.content
                    break

            # Store conversation messages in communication service
            agent_content = (
                agent_response.get("content", "")
                if isinstance(agent_response, dict)
                else str(agent_response)
            )
            await store_chat_messages(
                conversation_id=conversation_id,
                user_message=last_user_message,
                agent_response=agent_content,
                user_id=user_id,
            )

        logger.info(
            "Enhanced chat message sent and stored successfully",
            extra={
                "request_id": request_id,
                "session_id": session_id,
                "conversation_id": conversation_id,
                "run_id": run_id,
                "success": success,
                "operation": "send_enhanced_chat_message",
            },
        )

        return EnhancedChatResponse(
            success=success,
            message=message,
            agent_response=agent_response,
            run_id=run_id,
            final=final,
        )

    except Exception as e:
        logger.error(
            f"Error sending enhanced chat message: {str(e)}",
            extra={
                "request_id": request_id,
                "session_id": session_id,
                "operation": "send_enhanced_chat_message",
                "error": str(e),
                "error_type": type(e).__name__,
            },
        )

        raise HTTPException(
            status_code=500, detail=f"Failed to send enhanced chat message: {str(e)}"
        )
