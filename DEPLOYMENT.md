# Developer API Gateway - Deployment Guide

This guide provides comprehensive instructions for deploying the Developer API Gateway using Docker in both development and production environments.

## 🚀 Quick Start

### Prerequisites

- **Docker** (20.10+) and **Docker Compose** (2.0+)
- **Git** for cloning the repository
- **OpenSSL** for generating secrets (optional)

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd developer-api-gateway

# Set up environment configuration
./setup-env.sh

# Review the generated .env file
cat .env
```

### 2. Development Deployment

```bash
# Start development environment
./deploy.sh up

# View logs
./deploy.sh logs

# Check status
./deploy.sh status
```

### 3. Production Deployment

```bash
# Set up production environment
ENV=production ./setup-env.sh

# Deploy to production
./deploy.sh --env production up

# Monitor services
./deploy.sh --env production status
```

## 📋 Detailed Deployment Instructions

### Development Environment

The development environment is optimized for local development with hot reloading and debug logging.

#### Features:
- Hot reloading enabled
- Debug logging
- Development dependencies included
- Relaxed security settings
- Volume mounting for code changes

#### Deployment:

```bash
# Option 1: Using deployment script (recommended)
./deploy.sh up

# Option 2: Using docker-compose directly
docker-compose up --build -d

# Option 3: Using development Dockerfile
docker build -f Dockerfile.dev -t developer-api-gateway:dev .
docker run -p 8001:8001 developer-api-gateway:dev
```

### Production Environment

The production environment is optimized for performance, security, and reliability.

#### Features:
- Multi-stage build for smaller images
- Non-root user execution
- Read-only root filesystem
- Resource limits
- Health checks
- Structured JSON logging
- Security hardening

#### Deployment:

```bash
# Option 1: Using deployment script (recommended)
./deploy.sh --env production --pull up

# Option 2: Using production docker-compose
docker-compose -f docker-compose.prod.yml up --build -d

# Option 3: Manual build and run
docker build --target production -t developer-api-gateway:prod .
docker run -p 8001:8001 --env-file .env developer-api-gateway:prod
```

## 🔧 Configuration

### Environment Variables

All configuration is done through environment variables. See `.env.example` for all available options.

#### Required Variables:

```bash
# Application
ENV=production
JWT_SECRET_KEY=your-32-character-secret-key

# Redis (required)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# Kafka (required)
KAFKA_BOOTSTRAP_SERVERS=kafka:9092

# Services
USER_SERVICE_HOST=user_service
COMMUNICATION_SERVICE_HOST=admin_service
AGENT_SERVICE_URL=http://agent-service:8000
```

#### Optional Variables:

```bash
# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_ASYNC_ENABLED=true

# CORS (production)
CORS_ORIGINS=["https://yourdomain.com"]

# Repository
REPO_URL=https://github.com/your/repo
GIT_TOKEN=your-git-token
```

### Docker Compose Files

- `docker-compose.yml` - Development environment
- `docker-compose.prod.yml` - Production environment

### Dockerfiles

- `Dockerfile` - Multi-stage production build
- `Dockerfile.dev` - Development build with hot reloading

## 🛠️ Deployment Scripts

### `deploy.sh`

Main deployment script with comprehensive options:

```bash
# Basic usage
./deploy.sh [OPTIONS] COMMAND

# Commands
./deploy.sh up          # Start services
./deploy.sh down        # Stop services
./deploy.sh restart     # Restart services
./deploy.sh logs        # Show logs
./deploy.sh status      # Show status
./deploy.sh backup      # Backup data
./deploy.sh clean       # Clean up

# Options
--env production        # Use production environment
--pull                  # Pull latest images
--no-build             # Skip building
--logs                 # Show logs after start
--backup               # Create backup before operation
```

### `setup-env.sh`

Interactive environment setup script:

```bash
./setup-env.sh
```

Creates `.env` file from `.env.example` with guided configuration.

### `docker-build.sh`

Advanced build script with multiple options:

```bash
./docker-build.sh --env production --tag v1.0.0 --push
```

## 🔍 Monitoring and Health Checks

### Health Endpoints

- **Application Health**: `http://localhost:8001/health`
- **API Documentation**: `http://localhost:8001/docs`
- **Metrics**: `http://localhost:8001/metrics`

### Service Health Checks

All services include health checks:

```bash
# Check all services
docker-compose ps

# Check specific service health
docker inspect --format='{{.State.Health.Status}}' developer-api-gateway

# View health check logs
docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' developer-api-gateway
```

### Logging

#### Development:
- Text format for readability
- Debug level logging
- Console output with colors

#### Production:
- JSON format for log aggregation
- INFO level logging
- Structured logging with context
- Log rotation enabled

### Monitoring Commands

```bash
# View real-time logs
./deploy.sh logs

# Check resource usage
docker stats

# View service status
./deploy.sh status

# Check disk usage
docker system df
```

## 🔒 Security Considerations

### Production Security Features

1. **Non-root user execution**
2. **Read-only root filesystem**
3. **Security options**: `no-new-privileges`
4. **Resource limits**
5. **Network isolation**
6. **Secret management**

### Security Checklist

- [ ] Strong JWT secret key (32+ characters)
- [ ] Redis password configured
- [ ] CORS origins restricted
- [ ] Environment variables secured
- [ ] SSL/TLS certificates configured
- [ ] Firewall rules applied
- [ ] Regular security updates

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start

```bash
# Check logs
./deploy.sh logs

# Check service status
docker-compose ps

# Rebuild images
./deploy.sh --cleanup up
```

#### Database Connection Issues

```bash
# Check Redis connectivity
docker exec -it developer-api-redis redis-cli ping

# Check Kafka connectivity
docker exec -it developer-api-kafka kafka-broker-api-versions --bootstrap-server localhost:9092
```

#### Permission Issues

```bash
# Fix log directory permissions
sudo chown -R 1000:1000 ./logs

# Fix data directory permissions
sudo chown -R 1000:1000 ./data
```

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Set debug environment
export DEBUG=true
export LOG_LEVEL=DEBUG

# Restart with debug logging
./deploy.sh restart
```

## 📊 Performance Optimization

### Production Optimizations

1. **Multi-stage builds** for smaller images
2. **Resource limits** to prevent resource exhaustion
3. **Health checks** for automatic recovery
4. **Log rotation** to manage disk space
5. **Connection pooling** for databases
6. **Caching** with Redis

### Resource Limits

```yaml
# Production resource limits
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
    reservations:
      cpus: '1.0'
      memory: 1G
```

## 🔄 Backup and Recovery

### Backup

```bash
# Create backup
./deploy.sh backup

# Manual backup
docker run --rm -v developer-api-gateway_redis_data:/data -v $(pwd):/backup alpine tar czf /backup/redis_backup.tar.gz -C /data .
```

### Recovery

```bash
# Restore from backup
docker run --rm -v developer-api-gateway_redis_data:/data -v $(pwd):/backup alpine tar xzf /backup/redis_backup.tar.gz -C /data
```

## 🚀 Scaling and High Availability

### Horizontal Scaling

```bash
# Scale API gateway instances
docker-compose up --scale developer-api-gateway=3

# Use load balancer (nginx, traefik, etc.)
```

### High Availability Setup

1. **Multiple instances** behind load balancer
2. **Redis cluster** for session storage
3. **Kafka cluster** for message processing
4. **Health checks** for automatic failover
5. **Monitoring** and alerting

## 📝 Maintenance

### Regular Maintenance Tasks

```bash
# Update images
./deploy.sh --pull restart

# Clean up unused resources
docker system prune -f

# Backup data
./deploy.sh backup

# Check logs for errors
./deploy.sh logs | grep ERROR
```

### Updates and Upgrades

```bash
# Pull latest code
git pull origin main

# Rebuild and restart
./deploy.sh --cleanup --build up

# Verify deployment
./deploy.sh status
```

---

For additional support, check the main [README.md](README.md) or create an issue in the repository.
