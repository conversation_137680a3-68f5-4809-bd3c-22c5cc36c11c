# Development Dockerfile for faster iteration
FROM python:3.13-slim

# Build arguments
ARG ENV=development

# Set environment variables for development
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    ENV=${ENV} \
    POETRY_VERSION=1.8.3 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false \
    # FastAPI specific settings
    FASTAPI_ENV=${ENV} \
    # Application settings
    APP_HOST=0.0.0.0 \
    APP_PORT=8001 \
    # Development settings
    PYTHONPATH=/app \
    # Logging settings for development
    LOG_LEVEL=DEBUG \
    LOG_FORMAT=text \
    LOG_INCLUDE_SOURCE=true \
    # Development performance settings
    UVICORN_RELOAD=true \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Add poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    curl \
    git \
    build-essential \
    gcc \
    g++ \
    ca-certificates \
    netcat-openbsd \
    procps \
    vim \
    less \
    pkg-config \
    libffi-dev \
    libssl-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install poetry
RUN curl -sSL https://install.python-poetry.org | python3 - \
    && chmod +x $POETRY_HOME/bin/poetry

# Create non-root user for security (even in development)
RUN groupadd -r appuser --gid=1000 \
    && useradd -r -g appuser --uid=1000 --home-dir=/app --shell=/bin/bash appuser

# Set working directory
WORKDIR /app

# Copy poetry configuration files
COPY pyproject.toml poetry.lock ./

# Configure poetry for development
RUN poetry config virtualenvs.create false

# Install all dependencies (including dev dependencies for development)
RUN poetry install --no-root

# Copy application code with proper ownership
COPY --chown=appuser:appuser . .

# Create necessary directories and set permissions
RUN mkdir -p /app/logs /app/tmp /app/data \
    && chown -R appuser:appuser /app

# Generate gRPC code as root (before switching to appuser)
RUN poetry run python -m app.scripts.generate_grpc || echo "gRPC generation skipped (optional)"

# Switch to non-root user
USER appuser

# Create a development startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "Starting Developer API Gateway (Development Mode)..."\n\
echo "Environment: ${ENV}"\n\
echo "Host: ${APP_HOST}"\n\
echo "Port: ${APP_PORT}"\n\
echo "Reload: ${UVICORN_RELOAD}"\n\
\n\
# Start the application with development settings\n\
exec poetry run uvicorn app.main:app \\\n\
    --host ${APP_HOST} \\\n\
    --port ${APP_PORT} \\\n\
    --reload \\\n\
    --log-level ${LOG_LEVEL:-debug} \\\n\
    --access-log \\\n\
    --use-colors \\\n\
    --loop uvloop \\\n\
    --http httptools\n\
' > /app/start-dev.sh && chmod +x /app/start-dev.sh

# Health check for container orchestration
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:${APP_PORT}/health || exit 1

# Expose port
EXPOSE ${APP_PORT}

# Use the development startup script
CMD ["/app/start-dev.sh"]
