#!/bin/bash

# Cleanup script for developer-api-gateway
# This script removes unnecessary files and organizes the project structure

echo "🧹 Starting cleanup of developer-api-gateway..."

# Remove Python cache files
echo "📁 Removing Python cache files..."
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -type f -delete 2>/dev/null || true
find . -name "*.pyo" -type f -delete 2>/dev/null || true
find . -name "*.pyd" -type f -delete 2>/dev/null || true

# Remove IDE and editor files
echo "💻 Removing IDE and editor files..."
find . -name ".vscode" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".idea" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.swp" -type f -delete 2>/dev/null || true
find . -name "*.swo" -type f -delete 2>/dev/null || true
find . -name "*~" -type f -delete 2>/dev/null || true

# Remove OS-specific files
echo "🖥️  Removing OS-specific files..."
find . -name ".DS_Store" -type f -delete 2>/dev/null || true
find . -name "Thumbs.db" -type f -delete 2>/dev/null || true
find . -name "desktop.ini" -type f -delete 2>/dev/null || true

# Remove temporary files
echo "🗑️  Removing temporary files..."
find . -name "*.tmp" -type f -delete 2>/dev/null || true
find . -name "*.temp" -type f -delete 2>/dev/null || true
find . -name "*.log" -type f -delete 2>/dev/null || true

# Remove build artifacts
echo "🔨 Removing build artifacts..."
find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "dist" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.egg-info" -type d -exec rm -rf {} + 2>/dev/null || true

# Check for empty directories and list them
echo "📂 Checking for empty directories..."
empty_dirs=$(find . -type d -empty 2>/dev/null | grep -v "\.git" | head -10)
if [ -n "$empty_dirs" ]; then
    echo "Found empty directories:"
    echo "$empty_dirs"
    echo "You may want to remove these manually if they're not needed."
else
    echo "No empty directories found."
fi

# Verify important files exist
echo "✅ Verifying important files exist..."
important_files=(
    "app/main.py"
    "app/core/config.py"
    "app/services/a2a_server.py"
    "app/services/a2a_client.py"
    "app/services/a2a_agent_manager.py"
    "app/api/routers/a2a_routes.py"
    "app/api/routers/a2a_client_routes.py"
    "app/schemas/a2a_protocol.py"
    "app/examples/a2a_client_example.py"
    "test_a2a_client.py"
    "README.md"
    "A2A_IMPLEMENTATION.md"
    "A2A_CLIENT_GUIDE.md"
    "FOLDER_STRUCTURE.md"
)

missing_files=()
for file in "${important_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ All important files are present."
else
    echo "⚠️  Missing important files:"
    printf '%s\n' "${missing_files[@]}"
fi

# Check Python syntax for key files
echo "🐍 Checking Python syntax..."
python_files=(
    "app/main.py"
    "app/services/a2a_server.py"
    "app/services/a2a_client.py"
    "app/services/a2a_agent_manager.py"
    "app/api/routers/a2a_client_routes.py"
    "test_a2a_client.py"
)

syntax_errors=()
for file in "${python_files[@]}"; do
    if [ -f "$file" ]; then
        if ! python3 -m py_compile "$file" 2>/dev/null; then
            syntax_errors+=("$file")
        fi
    fi
done

if [ ${#syntax_errors[@]} -eq 0 ]; then
    echo "✅ All Python files have valid syntax."
else
    echo "❌ Python syntax errors found in:"
    printf '%s\n' "${syntax_errors[@]}"
fi

# Display final folder structure
echo "📁 Current folder structure:"
tree -I '__pycache__|*.pyc|.git' -L 3 . 2>/dev/null || find . -type d -not -path '*/\.*' -not -path '*/__pycache__' | head -20 | sort

# Summary
echo ""
echo "🎉 Cleanup completed!"
echo ""
echo "📊 Summary:"
echo "  ✅ Removed Python cache files"
echo "  ✅ Removed IDE/editor files"
echo "  ✅ Removed OS-specific files"
echo "  ✅ Removed temporary files"
echo "  ✅ Removed build artifacts"
echo "  ✅ Verified important files"
echo "  ✅ Checked Python syntax"
echo ""
echo "🚀 Your developer-api-gateway is now clean and organized!"
echo ""
echo "Next steps:"
echo "  1. Run: uvicorn app.main:app --host 0.0.0.0 --port 8000"
echo "  2. Test: python test_a2a_client.py"
echo "  3. Try examples: python -m app.examples.a2a_client_example"
echo "  4. View docs: http://localhost:8000/docs"
echo ""
