[tool.poetry]
name = "developer-api-gateway"
version = "1.0.0"
description = "Developer API Gateway for accessing platform services and resources"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.13"
uvicorn = "^0.23.2"
pydantic = "^2.4.2"
pydantic-settings = "^2.0.3"
python-jose = "^3.3.0"
python-multipart = "^0.0.6"
redis = "^5.0.1"
structlog = "^23.2.0"
email-validator = "^2.1.0.post1"
python-dotenv = "^1.0.0"
requests = "^2.31.0"
fastapi = "^0.115.12"
aiokafka = "^0.12.0"
a2a-sdk = "^0.2.5"
grpcio = "^1.72.1"
grpcio-tools = "^1.72.1"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.10.1"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.6.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
